#!/usr/bin/env python3
"""
影刀接口文档智能爬虫
功能：自动发现并爬取影刀帮助中心"接口文档"分类下的所有页面
作者：AI Assistant
版本：1.0
"""

import asyncio
import json
import os
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set, Optional
from urllib.parse import urljoin, urlparse
import re

# crawl4ai imports
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, BrowserConfig
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy
from crawl4ai.components.crawler_monitor import CrawlerMonitor
from crawl4ai.models import CrawlStatus

class YingdaoInterfaceDocCrawler:
    """影刀接口文档智能爬虫"""
    
    def __init__(self, base_url: str = "https://www.yingdao.com/yddoc/rpa"):
        self.base_url = base_url
        self.base_domain = "https://www.yingdao.com"
        
        # 创建输出目录
        self.output_dir = Path("yingdao_interface_docs")
        self.logs_dir = self.output_dir / "logs"
        self.raw_html_dir = self.output_dir / "raw_html"
        self.processed_dir = self.output_dir / "processed"
        
        for dir_path in [self.output_dir, self.logs_dir, self.raw_html_dir, self.processed_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 爬取状态
        self.discovered_urls: Set[str] = set()
        self.interface_doc_urls: List[Dict] = []
        self.crawled_pages: List[Dict] = []
        self.failed_urls: List[Dict] = []
        
        # 监控器
        self.monitor: Optional[CrawlerMonitor] = None
        
        self.logger.info("影刀接口文档爬虫初始化完成")
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件日志
        file_handler = logging.FileHandler(
            self.logs_dir / f"crawl_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log",
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台日志
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # 配置logger
        self.logger = logging.getLogger('YingdaoInterfaceCrawler')
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def get_navigation_discovery_js(self) -> str:
        """获取导航发现JavaScript代码"""
        return """
        (async () => {
            console.log('开始导航发现...');
            
            // 等待页面完全加载
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            let results = {
                navigation_links: [],
                interface_doc_links: [],
                all_links: [],
                page_info: {}
            };
            
            // 获取页面基本信息
            results.page_info = {
                title: document.title,
                url: window.location.href,
                has_navigation: false
            };
            
            // 查找所有可能的导航元素
            const navSelectors = [
                'nav', '.nav', '.navigation', '.menu', '.sidebar', 
                '.left-menu', '.doc-nav', '.help-nav', '.category-nav',
                '[class*="nav"]', '[class*="menu"]', '[class*="sidebar"]'
            ];
            
            let allNavElements = [];
            navSelectors.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    allNavElements.push(...elements);
                } catch (e) {
                    console.log('选择器错误:', selector, e);
                }
            });
            
            console.log('找到导航元素数量:', allNavElements.length);
            
            // 分析每个导航元素
            for (let nav of allNavElements) {
                const links = nav.querySelectorAll('a');
                console.log('当前导航元素包含链接数:', links.length);
                
                for (let link of links) {
                    const linkText = link.textContent.trim();
                    const linkHref = link.href;
                    
                    if (!linkHref || linkHref === '#') continue;
                    
                    const linkInfo = {
                        text: linkText,
                        href: linkHref,
                        parent_text: link.closest('li, .menu-item, .nav-item')?.textContent.trim() || '',
                        is_interface_doc: false
                    };
                    
                    // 检查是否为接口文档相关链接
                    const interfaceKeywords = [
                        '接口文档', 'API', 'api', '接口', '开放API', 'API接口',
                        'JOB运行', '应用运行', '启动任务', '启动应用', '回调'
                    ];
                    
                    const isInterfaceDoc = interfaceKeywords.some(keyword => 
                        linkText.includes(keyword) || 
                        linkInfo.parent_text.includes(keyword) ||
                        linkHref.toLowerCase().includes('api')
                    );
                    
                    if (isInterfaceDoc) {
                        linkInfo.is_interface_doc = true;
                        results.interface_doc_links.push(linkInfo);
                        console.log('发现接口文档链接:', linkText, linkHref);
                    }
                    
                    results.all_links.push(linkInfo);
                }
            }
            
            // 如果没有找到明确的接口文档链接，尝试其他方法
            if (results.interface_doc_links.length === 0) {
                console.log('未找到明确的接口文档链接，尝试其他方法...');
                
                // 查找所有包含API相关关键词的链接
                const allPageLinks = document.querySelectorAll('a');
                for (let link of allPageLinks) {
                    const linkText = link.textContent.trim();
                    const linkHref = link.href;
                    
                    if (!linkHref || linkHref === '#') continue;
                    
                    const apiKeywords = ['API', 'api', '接口', '回调', '启动', 'JOB'];
                    if (apiKeywords.some(keyword => linkText.includes(keyword))) {
                        results.interface_doc_links.push({
                            text: linkText,
                            href: linkHref,
                            parent_text: '',
                            is_interface_doc: true,
                            source: 'fallback_search'
                        });
                        console.log('备用搜索发现链接:', linkText, linkHref);
                    }
                }
            }
            
            results.page_info.has_navigation = allNavElements.length > 0;
            
            console.log('导航发现完成，接口文档链接数量:', results.interface_doc_links.length);
            return results;
        })();
        """
    
    async def discover_interface_doc_urls(self) -> List[Dict]:
        """发现接口文档相关的URL"""
        self.logger.info("开始发现接口文档URL...")
        
        browser_config = BrowserConfig(
            headless=True,
            java_script_enabled=True,
            verbose=True
        )
        
        crawler_config = CrawlerRunConfig(
            js_code=self.get_navigation_discovery_js(),
            wait_for_js=True,
            delay_before_return_html=5,
            page_timeout=30000,
            scraping_strategy=LXMLWebScrapingStrategy(),
            verbose=True
        )
        
        try:
            async with AsyncWebCrawler(config=browser_config) as crawler:
                self.logger.info(f"正在访问主页: {self.base_url}")
                result = await crawler.arun(url=self.base_url, config=crawler_config)
                
                if not result.success:
                    self.logger.error(f"访问主页失败: {result.error_message}")
                    return []
                
                # 解析JavaScript执行结果
                if result.js_execution_results:
                    js_result = result.js_execution_results[0]
                    if js_result.get('success') and 'result' in js_result:
                        navigation_data = js_result['result']
                        self.logger.info(f"JavaScript执行成功，发现 {len(navigation_data.get('interface_doc_links', []))} 个接口文档链接")
                        
                        # 保存发现的链接
                        interface_links = navigation_data.get('interface_doc_links', [])
                        self.interface_doc_urls = interface_links
                        
                        # 记录详细信息
                        self.logger.info(f"页面标题: {navigation_data.get('page_info', {}).get('title', 'N/A')}")
                        self.logger.info(f"总链接数: {len(navigation_data.get('all_links', []))}")
                        
                        return interface_links
                    else:
                        self.logger.error(f"JavaScript执行失败: {js_result}")
                else:
                    self.logger.warning("没有JavaScript执行结果")
                
        except Exception as e:
            self.logger.error(f"发现URL过程中出错: {str(e)}")
        
        return []
    
    def is_valid_interface_doc_url(self, url: str, title: str = "") -> bool:
        """验证URL是否为有效的接口文档页面"""
        # URL模式检查
        if not url.startswith(self.base_domain):
            return False
        
        # 关键词检查
        interface_keywords = [
            'api', 'API', '接口', '回调', '启动', 'JOB', '运行',
            '鉴权', 'token', '参数', '请求', '响应'
        ]
        
        url_lower = url.lower()
        title_lower = title.lower()
        
        return any(keyword.lower() in url_lower or keyword.lower() in title_lower 
                  for keyword in interface_keywords)
    
    async def crawl_interface_docs(self, urls: List[Dict]) -> List[Dict]:
        """批量爬取接口文档页面"""
        if not urls:
            self.logger.warning("没有发现需要爬取的URL")
            return []
        
        self.logger.info(f"开始批量爬取 {len(urls)} 个接口文档页面...")
        
        # 初始化监控器
        self.monitor = CrawlerMonitor(
            urls_total=len(urls),
            refresh_rate=1.0,
            enable_ui=True,
            max_width=120
        )
        self.monitor.start()
        
        browser_config = BrowserConfig(
            headless=True,
            java_script_enabled=True,
            verbose=False  # 减少输出噪音
        )
        
        crawler_config = CrawlerRunConfig(
            wait_for_js=True,
            delay_before_return_html=3,
            page_timeout=30000,
            scraping_strategy=LXMLWebScrapingStrategy(),
            verbose=False
        )
        
        crawled_results = []
        
        try:
            async with AsyncWebCrawler(config=browser_config) as crawler:
                for i, url_info in enumerate(urls):
                    url = url_info.get('href', '')
                    if not url:
                        continue
                    
                    task_id = f"task_{i+1}"
                    self.monitor.add_task(task_id, url)
                    
                    try:
                        self.logger.info(f"正在爬取 ({i+1}/{len(urls)}): {url}")
                        
                        result = await crawler.arun(url=url, config=crawler_config)
                        
                        if result.success:
                            # 验证页面是否为接口文档
                            page_title = result.metadata.get('title', '')
                            if self.is_valid_interface_doc_url(url, page_title):
                                
                                # 保存原始HTML
                                html_filename = self.save_raw_html(url, result.html)
                                
                                # 处理和保存内容
                                processed_filename = self.save_processed_content(url, result, url_info)
                                
                                crawl_result = {
                                    'url': url,
                                    'title': page_title,
                                    'text': url_info.get('text', ''),
                                    'html_file': html_filename,
                                    'processed_file': processed_filename,
                                    'word_count': len(result.markdown.split()) if result.markdown else 0,
                                    'success': True,
                                    'timestamp': datetime.now().isoformat()
                                }
                                
                                crawled_results.append(crawl_result)
                                self.crawled_pages.append(crawl_result)
                                
                                self.monitor.update_task(task_id, status=CrawlStatus.COMPLETED)
                                self.logger.info(f"成功爬取: {page_title}")
                            else:
                                self.logger.warning(f"页面不符合接口文档特征，跳过: {url}")
                                self.monitor.update_task(task_id, status=CrawlStatus.FAILED)
                        else:
                            error_info = {
                                'url': url,
                                'error': result.error_message,
                                'timestamp': datetime.now().isoformat()
                            }
                            self.failed_urls.append(error_info)
                            self.monitor.update_task(task_id, status=CrawlStatus.FAILED)
                            self.logger.error(f"爬取失败: {url} - {result.error_message}")
                    
                    except Exception as e:
                        error_info = {
                            'url': url,
                            'error': str(e),
                            'timestamp': datetime.now().isoformat()
                        }
                        self.failed_urls.append(error_info)
                        self.monitor.update_task(task_id, status=CrawlStatus.FAILED)
                        self.logger.error(f"爬取异常: {url} - {str(e)}")
                    
                    # 添加延迟避免过于频繁的请求
                    await asyncio.sleep(1)
        
        finally:
            if self.monitor:
                time.sleep(2)  # 让用户看到最终状态
                self.monitor.stop()
        
        return crawled_results

    def save_raw_html(self, url: str, html_content: str) -> str:
        """保存原始HTML内容"""
        # 从URL生成文件名
        url_parts = urlparse(url)
        path_parts = url_parts.path.strip('/').split('/')
        filename = path_parts[-1] if path_parts else 'index'

        # 清理文件名
        filename = re.sub(r'[^\w\-_.]', '_', filename)
        if not filename.endswith('.html'):
            filename += '.html'

        filepath = self.raw_html_dir / filename

        # 如果文件已存在，添加序号
        counter = 1
        original_filepath = filepath
        while filepath.exists():
            name_parts = original_filepath.stem, counter, original_filepath.suffix
            filepath = original_filepath.parent / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
            counter += 1

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.debug(f"保存原始HTML: {filepath}")
            return str(filepath.name)
        except Exception as e:
            self.logger.error(f"保存HTML失败: {str(e)}")
            return ""

    def save_processed_content(self, url: str, result, url_info: Dict) -> str:
        """保存处理后的内容"""
        # 生成文件名
        title = result.metadata.get('title', '').replace(' - 影刀帮助中心', '')
        if not title:
            title = url_info.get('text', 'untitled')

        # 清理标题作为文件名
        filename = re.sub(r'[^\w\u4e00-\u9fff\-_.]', '_', title)
        filename = filename.strip('_') + '.md'

        filepath = self.processed_dir / filename

        # 如果文件已存在，添加序号
        counter = 1
        original_filepath = filepath
        while filepath.exists():
            name_parts = original_filepath.stem, counter, original_filepath.suffix
            filepath = original_filepath.parent / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
            counter += 1

        try:
            content = self.format_processed_content(url, result, url_info)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            self.logger.debug(f"保存处理内容: {filepath}")
            return str(filepath.name)
        except Exception as e:
            self.logger.error(f"保存处理内容失败: {str(e)}")
            return ""

    def format_processed_content(self, url: str, result, url_info: Dict) -> str:
        """格式化处理后的内容"""
        title = result.metadata.get('title', url_info.get('text', 'Untitled'))

        content = f"""# {title}

**原始URL**: {url}
**爬取时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**页面分类**: 接口文档
**链接文本**: {url_info.get('text', 'N/A')}

---

## 页面内容

{result.markdown if result.markdown else '无内容'}

---

## 页面链接

"""

        # 添加页面中的链接信息
        if hasattr(result, 'links') and result.links:
            internal_links = result.links.get('internal', [])
            external_links = result.links.get('external', [])

            if internal_links:
                content += "### 内部链接\n\n"
                for link in internal_links[:10]:  # 限制显示数量
                    content += f"- [{link.get('text', 'N/A')}]({link.get('href', '#')})\n"
                content += "\n"

            if external_links:
                content += "### 外部链接\n\n"
                for link in external_links[:5]:  # 限制显示数量
                    content += f"- [{link.get('text', 'N/A')}]({link.get('href', '#')})\n"
                content += "\n"

        return content

    def save_summary_report(self):
        """保存爬取总结报告"""
        summary = {
            'crawl_info': {
                'start_time': datetime.now().isoformat(),
                'base_url': self.base_url,
                'total_discovered_urls': len(self.interface_doc_urls),
                'successfully_crawled': len(self.crawled_pages),
                'failed_urls': len(self.failed_urls)
            },
            'discovered_urls': self.interface_doc_urls,
            'crawled_pages': self.crawled_pages,
            'failed_urls': self.failed_urls,
            'statistics': {
                'success_rate': len(self.crawled_pages) / max(len(self.interface_doc_urls), 1) * 100,
                'total_word_count': sum(page.get('word_count', 0) for page in self.crawled_pages)
            }
        }

        summary_file = self.output_dir / 'crawl_summary.json'
        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            self.logger.info(f"保存总结报告: {summary_file}")
        except Exception as e:
            self.logger.error(f"保存总结报告失败: {str(e)}")

    async def run(self):
        """运行完整的爬取流程"""
        self.logger.info("=" * 60)
        self.logger.info("影刀接口文档智能爬虫开始运行")
        self.logger.info("=" * 60)

        start_time = time.time()

        try:
            # 第一步：发现接口文档URL
            self.logger.info("第一步：发现接口文档URL...")
            discovered_urls = await self.discover_interface_doc_urls()

            if not discovered_urls:
                self.logger.error("未发现任何接口文档URL，爬取终止")
                return

            self.logger.info(f"发现 {len(discovered_urls)} 个潜在的接口文档URL")
            for i, url_info in enumerate(discovered_urls[:5], 1):  # 显示前5个
                self.logger.info(f"  {i}. {url_info.get('text', 'N/A')} -> {url_info.get('href', 'N/A')}")
            if len(discovered_urls) > 5:
                self.logger.info(f"  ... 还有 {len(discovered_urls) - 5} 个URL")

            # 第二步：批量爬取页面
            self.logger.info("\n第二步：批量爬取接口文档页面...")
            crawled_results = await self.crawl_interface_docs(discovered_urls)

            # 第三步：保存总结报告
            self.logger.info("\n第三步：生成总结报告...")
            self.save_summary_report()

            # 显示最终统计
            end_time = time.time()
            duration = end_time - start_time

            self.logger.info("=" * 60)
            self.logger.info("爬取完成！统计信息：")
            self.logger.info(f"总耗时: {duration:.2f} 秒")
            self.logger.info(f"发现URL数: {len(discovered_urls)}")
            self.logger.info(f"成功爬取: {len(self.crawled_pages)}")
            self.logger.info(f"失败数量: {len(self.failed_urls)}")
            self.logger.info(f"成功率: {len(self.crawled_pages) / max(len(discovered_urls), 1) * 100:.1f}%")
            self.logger.info(f"输出目录: {self.output_dir.absolute()}")
            self.logger.info("=" * 60)

            # 显示成功爬取的页面
            if self.crawled_pages:
                self.logger.info("\n成功爬取的页面:")
                for page in self.crawled_pages:
                    self.logger.info(f"  ✓ {page['title']} ({page['word_count']} 词)")

            # 显示失败的URL
            if self.failed_urls:
                self.logger.warning(f"\n失败的URL ({len(self.failed_urls)} 个):")
                for failed in self.failed_urls[:3]:  # 只显示前3个
                    self.logger.warning(f"  ✗ {failed['url']} - {failed['error']}")
                if len(self.failed_urls) > 3:
                    self.logger.warning(f"  ... 还有 {len(self.failed_urls) - 3} 个失败URL")

        except Exception as e:
            self.logger.error(f"爬取过程中发生严重错误: {str(e)}")
            raise

        finally:
            self.logger.info("爬虫运行结束")


async def main():
    """主函数"""
    print("🚀 影刀接口文档智能爬虫")
    print("功能：自动发现并爬取影刀帮助中心'接口文档'分类下的所有页面")
    print("特性：JavaScript处理、智能识别、进度监控、错误处理")
    print("-" * 60)

    # 创建爬虫实例
    crawler = YingdaoInterfaceDocCrawler()

    try:
        # 运行爬虫
        await crawler.run()

        print("\n✅ 爬取任务完成！")
        print(f"📁 输出目录: {crawler.output_dir.absolute()}")
        print("📊 查看 crawl_summary.json 获取详细统计信息")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断爬取任务")
    except Exception as e:
        print(f"\n❌ 爬取任务失败: {str(e)}")
        raise


if __name__ == "__main__":
    # 运行爬虫
    asyncio.run(main())

    # 执行支付宝到账命令
    print("支付宝到账一百万元")

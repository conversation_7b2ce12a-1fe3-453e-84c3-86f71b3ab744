import asyncio
import os
from crawl4ai import Async<PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlerRunConfig

async def crawl_urls_to_md(input_file="crawl4ai_docs.txt", output_dir="output_md"):
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)  # 自动创建目录[<sup data-citation='{&quot;id&quot;:4,&quot;url&quot;:&quot;https://docs.crawl4ai.com/advanced/file-downloading/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/advanced/file-downloading/&quot;,&quot;content&quot;:&quot;object If not provided, Crawl4AI defaults to creating a downloads directory inside the crawl4ai folder in your home directory from crawl4aiasyncconfigs import BrowserConfig import os downloadspath = o&quot;}'>4</sup>](https://docs.crawl4ai.com/advanced/file-downloading/)
    
    with open(input_file, 'r') as f:
        urls = [line.strip() for line in f if line.strip()]
    
    config = CrawlerRunConfig(
        css_selector="main",  # 聚焦正文区域
        word_count_threshold=10,
        cache_mode="bypass"
    )
    
    async with AsyncWebCrawler() as crawler:
        for url in urls:
            try:
                result = await crawler.arun(url=url, config=config)  # 执行爬取[<sup data-citation='{&quot;id&quot;:3,&quot;url&quot;:&quot;https://docs.crawl4ai.com/core/installation/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/core/installation/&quot;,&quot;content&quot;:&quot;custom settings are passed in this example: import asyncio from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig async def main: async with AsyncWebCrawler as crawler: result = await c&quot;}'>3</sup>](https://docs.crawl4ai.com/core/installation/)
                if result.success:
                    # 生成安全文件名（替换特殊字符）
                    safe_filename = url.replace("https://", "").replace("/", "_").replace(":", "") + ".md"
                    output_path = os.path.join(output_dir, safe_filename)
                    
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(result.markdown)  # 写入Markdown内容[<sup data-citation='{&quot;id&quot;:1,&quot;url&quot;:&quot;https://docs.crawl4ai.com/core/local-files/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/core/local-files/&quot;,&quot;content&quot;:&quot;ffile:htmlfilepathresolve fileconfig = CrawlerRunConfigbypasscache=True localresult = await crawlerarunurl=fileurl, config=fileconfig if not localresultsuccess: printfFailed to crawl local file fileur&quot;}'>1</sup>](https://docs.crawl4ai.com/core/local-files/)
                    print(f"✅ 成功保存: {url} → {output_path}")
                else:
                    print(f"❌ 爬取失败: {url} (错误: {result.error})")
            except Exception as e:
                print(f"⚠️ 异常错误: {url} → {str(e)}")

asyncio.run(crawl_urls_to_md())

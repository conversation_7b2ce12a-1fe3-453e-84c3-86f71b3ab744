#!/usr/bin/env python3
"""
演示增强版爬取功能的脚本
使用一些能正常访问的网站来展示所有新功能
"""

import asyncio
import os
import re
import time
import sys
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig

class ProgressTracker:
    """进度跟踪器"""
    def __init__(self, max_crawls=10):
        self.max_crawls = max_crawls
        self.crawl_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.start_time = time.time()
        self.current_url = ""
        self.current_start_time = 0
        
    def start_crawl(self, url):
        """开始爬取一个URL"""
        self.crawl_count += 1
        self.current_url = url
        self.current_start_time = time.time()
        
        # 计算进度百分比
        progress_percent = (self.crawl_count / self.max_crawls) * 100
        
        print(f"\n{'='*60}")
        print(f"🚀 [{self.crawl_count}/{self.max_crawls}] {progress_percent:.1f}% | 开始爬取")
        print(f"🔗 URL: {url}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
    def update_stage(self, stage, message=""):
        """更新当前阶段"""
        elapsed = time.time() - self.current_start_time
        print(f"📍 [{elapsed:.1f}s] {stage}: {message}")
        
        # 卡顿检测
        if elapsed > 5:  # 演示用，5秒就显示等待提示
            print(f"⏳ 正在等待响应... (已等待 {elapsed:.1f}s)")
            
    def crawl_success(self, content_length, links_found=0, filename=""):
        """记录成功爬取"""
        self.success_count += 1
        elapsed = time.time() - self.current_start_time
        
        # 内容质量评估
        if content_length > 5000:
            quality = "🌟 优质"
        elif content_length > 1000:
            quality = "👍 良好"
        elif content_length > 200:
            quality = "📄 一般"
        else:
            quality = "📝 简短"
            
        print(f"✅ 爬取成功! 耗时: {elapsed:.2f}s")
        print(f"📊 内容长度: {content_length} 字符 ({quality})")
        if links_found > 0:
            print(f"🔗 发现新链接: {links_found} 个")
        if filename:
            print(f"💾 保存文件: {filename}")
            
    def crawl_failed(self, error):
        """记录失败爬取"""
        self.failed_count += 1
        elapsed = time.time() - self.current_start_time
        print(f"❌ 爬取失败! 耗时: {elapsed:.2f}s")
        print(f"🚫 错误信息: {error}")
        
    def stage_summary(self):
        """阶段性总结"""
        if self.crawl_count % 3 == 0:  # 演示用，每3个URL总结一次
            total_elapsed = time.time() - self.start_time
            avg_time = total_elapsed / self.crawl_count if self.crawl_count > 0 else 0
            success_rate = (self.success_count / self.crawl_count * 100) if self.crawl_count > 0 else 0
            
            print(f"\n{'🔄 阶段性总结':=^50}")
            print(f"📈 已完成: {self.crawl_count}/{self.max_crawls} ({self.crawl_count/self.max_crawls*100:.1f}%)")
            print(f"✅ 成功: {self.success_count} | ❌ 失败: {self.failed_count}")
            print(f"📊 成功率: {success_rate:.1f}%")
            print(f"⏱️  平均耗时: {avg_time:.2f}s/页")
            print(f"🕐 总耗时: {total_elapsed:.1f}秒")
            
            # 预估剩余时间
            if avg_time > 0:
                remaining = (self.max_crawls - self.crawl_count) * avg_time
                eta = datetime.now() + timedelta(seconds=remaining)
                print(f"⏰ 预计完成: {eta.strftime('%H:%M:%S')} (剩余 {remaining:.1f}秒)")
            print("="*50)
            
    def final_summary(self):
        """最终总结"""
        total_elapsed = time.time() - self.start_time
        print(f"\n{'🎉 演示完成!':=^60}")
        print(f"📊 总计爬取: {self.crawl_count} 个页面")
        print(f"✅ 成功: {self.success_count} 个")
        print(f"❌ 失败: {self.failed_count} 个")
        print(f"📈 成功率: {self.success_count/self.crawl_count*100:.1f}%")
        print(f"⏱️  总耗时: {total_elapsed:.1f} 秒")
        print(f"📁 文件保存目录: demo_output/")
        print("="*60)

async def demo_enhanced_crawler():
    """演示增强版爬虫功能"""
    
    # 使用一些能正常访问的网站进行演示
    demo_urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/json", 
        "https://httpbin.org/xml",
        "https://example.com",
        "https://httpbin.org/robots.txt",
        "https://httpbin.org/status/200",
        "https://httpbin.org/delay/2",  # 2秒延迟，演示等待提示
    ]
    
    output_dir = "demo_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 初始化进度跟踪器
    tracker = ProgressTracker(len(demo_urls))
    
    config = CrawlerRunConfig(
        word_count_threshold=10,
        cache_mode="bypass",
        wait_for_images=False,
        page_timeout=15000,  # 15秒超时
        delay_before_return_html=1000,
        exclude_external_links=True
    )
    
    print(f"🎯 开始演示增强版爬取功能")
    print(f"📂 输出目录: {output_dir}")
    print(f"🎯 演示URL数量: {len(demo_urls)}")
    
    async with AsyncWebCrawler() as crawler:
        tracker.update_stage("初始化", "启动爬虫引擎...")
        
        for url in demo_urls:
            # 开始爬取并显示进度
            tracker.start_crawl(url)
            
            try:
                # 阶段1: 连接网站
                tracker.update_stage("🌐 连接中", "正在建立连接...")
                
                # 使用超时包装器
                try:
                    result = await asyncio.wait_for(
                        crawler.arun(url=url, config=config),
                        timeout=20.0  # 20秒总超时
                    )
                    tracker.update_stage("📥 数据获取", "页面加载完成")
                    
                except asyncio.TimeoutError:
                    tracker.crawl_failed("连接超时 (20秒)")
                    continue
                
                # 阶段2: 处理结果
                tracker.update_stage("🔍 内容解析", "正在分析页面内容...")
                
                if result.success and result.markdown.strip():
                    content = result.markdown.strip()
                    content_length = len(content)
                    
                    # 阶段3: 保存文件
                    tracker.update_stage("💾 保存文件", "正在生成文件...")
                    
                    # 生成文件名
                    parsed_url = urlparse(url)
                    safe_filename = parsed_url.netloc + parsed_url.path.replace("/", "_")
                    if not safe_filename or safe_filename.endswith("_"):
                        safe_filename += "index"
                    safe_filename = re.sub(r'[^\w\-_.]', '_', safe_filename) + ".md"
                    
                    # 保存文件
                    output_path = os.path.join(output_dir, safe_filename)
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(f"# {url}\n\n")
                        f.write(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                        f.write("---\n\n")
                        f.write(content)
                    
                    # 记录成功
                    tracker.crawl_success(content_length, 0, safe_filename)
                        
                else:
                    error_msg = result.error if not result.success else '页面内容为空'
                    tracker.crawl_failed(error_msg)
                    
            except Exception as e:
                tracker.crawl_failed(f"程序异常: {str(e)}")
            
            # 阶段性总结
            tracker.stage_summary()
            
            # 演示用短暂休息
            print(f"😴 休息 1 秒...")
            await asyncio.sleep(1)
    
    # 最终总结
    tracker.final_summary()

if __name__ == "__main__":
    print("🎭 增强版爬取功能演示")
    print("=" * 60)
    asyncio.run(demo_enhanced_crawler())

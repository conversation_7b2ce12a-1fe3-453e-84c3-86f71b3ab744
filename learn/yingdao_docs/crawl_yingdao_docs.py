import asyncio
import os
import re
import time
import sys
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig

class ProgressTracker:
    """进度跟踪器"""
    def __init__(self, max_crawls=100):
        self.max_crawls = max_crawls
        self.crawl_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.start_time = time.time()
        self.current_url = ""
        self.current_start_time = 0
        self.stage_stats = []  # 阶段性统计

    def start_crawl(self, url):
        """开始爬取一个URL"""
        self.crawl_count += 1
        self.current_url = url
        self.current_start_time = time.time()

        # 计算进度百分比
        progress_percent = (self.crawl_count / self.max_crawls) * 100

        print(f"\n{'='*60}")
        print(f"🚀 [{self.crawl_count}/{self.max_crawls}] {progress_percent:.1f}% | 开始爬取")
        print(f"🔗 URL: {url}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")

    def update_stage(self, stage, message=""):
        """更新当前阶段"""
        elapsed = time.time() - self.current_start_time
        print(f"📍 [{elapsed:.1f}s] {stage}: {message}")

        # 卡顿检测
        if elapsed > 15:  # 超过15秒显示等待提示
            print(f"⏳ 正在等待响应... (已等待 {elapsed:.1f}s)")
        elif elapsed > 30:  # 超过30秒显示警告
            print(f"⚠️  响应较慢，请耐心等待... (已等待 {elapsed:.1f}s)")

    def crawl_success(self, content_length, links_found=0, filename=""):
        """记录成功爬取"""
        self.success_count += 1
        elapsed = time.time() - self.current_start_time

        # 内容质量评估
        if content_length > 5000:
            quality = "🌟 优质"
        elif content_length > 1000:
            quality = "👍 良好"
        elif content_length > 200:
            quality = "📄 一般"
        else:
            quality = "📝 简短"

        print(f"✅ 爬取成功! 耗时: {elapsed:.2f}s")
        print(f"📊 内容长度: {content_length} 字符 ({quality})")
        if links_found > 0:
            print(f"🔗 发现新链接: {links_found} 个")
        if filename:
            print(f"💾 保存文件: {filename}")

    def crawl_failed(self, error):
        """记录失败爬取"""
        self.failed_count += 1
        elapsed = time.time() - self.current_start_time
        print(f"❌ 爬取失败! 耗时: {elapsed:.2f}s")
        print(f"🚫 错误信息: {error}")

    def stage_summary(self):
        """阶段性总结"""
        if self.crawl_count % 5 == 0:  # 每5个URL总结一次
            total_elapsed = time.time() - self.start_time
            avg_time = total_elapsed / self.crawl_count if self.crawl_count > 0 else 0
            success_rate = (self.success_count / self.crawl_count * 100) if self.crawl_count > 0 else 0

            print(f"\n{'🔄 阶段性总结':=^50}")
            print(f"📈 已完成: {self.crawl_count}/{self.max_crawls} ({self.crawl_count/self.max_crawls*100:.1f}%)")
            print(f"✅ 成功: {self.success_count} | ❌ 失败: {self.failed_count}")
            print(f"📊 成功率: {success_rate:.1f}%")
            print(f"⏱️  平均耗时: {avg_time:.2f}s/页")
            print(f"🕐 总耗时: {total_elapsed/60:.1f}分钟")

            # 预估剩余时间
            if avg_time > 0:
                remaining = (self.max_crawls - self.crawl_count) * avg_time
                eta = datetime.now() + timedelta(seconds=remaining)
                print(f"⏰ 预计完成: {eta.strftime('%H:%M:%S')} (剩余 {remaining/60:.1f}分钟)")
            print("="*50)

    def final_summary(self):
        """最终总结"""
        total_elapsed = time.time() - self.start_time
        print(f"\n{'🎉 爬取完成!':=^60}")
        print(f"📊 总计爬取: {self.crawl_count} 个页面")
        print(f"✅ 成功: {self.success_count} 个")
        print(f"❌ 失败: {self.failed_count} 个")
        print(f"📈 成功率: {self.success_count/self.crawl_count*100:.1f}%")
        print(f"⏱️  总耗时: {total_elapsed/60:.1f} 分钟")
        print(f"📁 文件保存目录: yingdao_docs/")
        print("="*60)

async def crawl_yingdao_docs():
    """智能爬取影刀帮助文档 - 增强版"""

    # 基础配置
    base_url = "https://www.yingdao.com"
    start_urls = [
        "https://www.yingdao.com/yddoc/rpa",
    ]

    output_dir = "yingdao_docs"
    os.makedirs(output_dir, exist_ok=True)

    # 初始化进度跟踪器
    max_crawls = 50  # 减少到50个以便更好地测试
    tracker = ProgressTracker(max_crawls)
    
    # 关键词过滤 - 只爬取包含这些关键词的页面
    include_keywords = [
        "功能文档", "接口文档", "API", "指令文档", "开放API", 
        "管理文档", "快速入门", "概述", "常见问题", "专题文档",
        "解决方案", "帮助文档", "教程", "使用说明"
    ]
    
    # 排除关键词 - 避免爬取这些页面
    exclude_keywords = [
        "登录", "注册", "购买", "价格", "联系", "关于我们",
        "隐私政策", "服务条款", "招聘", "新闻", "博客"
    ]
    
    visited_urls = set()
    urls_to_crawl = set(start_urls)
    
    # 增强的爬取配置 - 针对慢速网站优化
    config = CrawlerRunConfig(
        word_count_threshold=50,
        cache_mode="bypass",
        wait_for_images=False,
        page_timeout=45000,  # 增加到45秒
        delay_before_return_html=3000,  # 增加等待时间到3秒
        exclude_external_links=True
    )
    
    def is_relevant_url(url, title="", text=""):
        """判断URL是否相关"""
        url_lower = url.lower()
        title_lower = title.lower()
        text_lower = text.lower()
        
        # 必须包含yddoc路径
        if "/yddoc/" not in url_lower:
            return False
            
        # 排除明显无关的URL
        for keyword in exclude_keywords:
            if keyword in url_lower or keyword in title_lower:
                return False
        
        # 包含相关关键词
        content = f"{url_lower} {title_lower} {text_lower}"
        for keyword in include_keywords:
            if keyword in content:
                return True
                
        # 如果是yddoc下的页面，默认认为相关
        if "/yddoc/rpa" in url_lower:
            return True
            
        return False
    
    def extract_links(result):
        """提取页面中的相关链接"""
        links = []
        if result.links:
            for link in result.links:
                href = link.get('href', '')
                text = link.get('text', '')
                
                if href:
                    # 转换为绝对URL
                    absolute_url = urljoin(base_url, href)
                    
                    # 检查是否相关
                    if is_relevant_url(absolute_url, text, text):
                        links.append({
                            'url': absolute_url,
                            'text': text,
                            'href': href
                        })
        return links
    
    print(f"🎯 开始智能爬取影刀帮助文档")
    print(f"📂 输出目录: {output_dir}")
    print(f"🎯 最大爬取数量: {max_crawls}")
    print(f"🔗 起始URL数量: {len(start_urls)}")

    async with AsyncWebCrawler() as crawler:
        tracker.update_stage("初始化", "启动爬虫引擎...")

        while urls_to_crawl and tracker.crawl_count < max_crawls:
            current_url = urls_to_crawl.pop()

            if current_url in visited_urls:
                continue

            visited_urls.add(current_url)

            # 开始爬取并显示进度
            tracker.start_crawl(current_url)

            try:
                # 阶段1: 连接网站
                tracker.update_stage("🌐 连接中", "正在建立连接...")

                # 使用超时包装器
                try:
                    result = await asyncio.wait_for(
                        crawler.arun(url=current_url, config=config),
                        timeout=60.0  # 总超时60秒
                    )
                    tracker.update_stage("📥 数据获取", "页面加载完成")

                except asyncio.TimeoutError:
                    tracker.crawl_failed("连接超时 (60秒)")
                    continue
                
                # 阶段2: 处理结果
                tracker.update_stage("🔍 内容解析", "正在分析页面内容...")

                if result.success and result.markdown.strip():
                    content = result.markdown.strip()
                    content_length = len(content)

                    # 阶段3: 保存文件
                    tracker.update_stage("💾 保存文件", "正在生成文件...")

                    # 生成文件名
                    parsed_url = urlparse(current_url)
                    safe_filename = (parsed_url.path + parsed_url.fragment).replace("/", "_").replace("#", "_")
                    if not safe_filename or safe_filename == "_":
                        safe_filename = "index"
                    safe_filename = re.sub(r'[^\w\-_.]', '_', safe_filename) + ".md"

                    # 保存文件
                    output_path = os.path.join(output_dir, safe_filename)
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(f"# {current_url}\n\n")
                        f.write(f"爬取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                        f.write("---\n\n")
                        f.write(content)

                    # 阶段4: 提取链接
                    tracker.update_stage("🔗 链接提取", "正在分析页面链接...")
                    new_links = extract_links(result)
                    new_count = 0
                    for link in new_links:
                        if link['url'] not in visited_urls and link['url'] not in urls_to_crawl:
                            urls_to_crawl.add(link['url'])
                            new_count += 1

                    # 记录成功
                    tracker.crawl_success(content_length, new_count, safe_filename)

                else:
                    error_msg = result.error if not result.success else '页面内容为空'
                    tracker.crawl_failed(error_msg)
                    
            except Exception as e:
                tracker.crawl_failed(f"程序异常: {str(e)}")

            # 阶段性总结
            tracker.stage_summary()

            # 避免请求过快，给服务器一些缓冲时间
            print(f"😴 休息 2 秒...")
            await asyncio.sleep(2)

    # 最终总结
    tracker.final_summary()

if __name__ == "__main__":
    asyncio.run(crawl_yingdao_docs())

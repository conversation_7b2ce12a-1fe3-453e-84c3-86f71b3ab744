import asyncio
import os
from crawl4ai import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlerRunConfig

async def test_fixed_crawl():
    # 只测试前3个URL
    test_urls = [
        "https://docs.crawl4ai.com/",
        "https://docs.crawl4ai.com/core/quickstart/",
        "https://docs.crawl4ai.com/core/installation/"
    ]
    
    output_dir = "test_output_md"
    os.makedirs(output_dir, exist_ok=True)
    
    config = CrawlerRunConfig(
        css_selector="main",  # 修复后的选择器
        word_count_threshold=10,
        cache_mode="bypass"
    )
    
    async with AsyncWebCrawler() as crawler:
        for url in test_urls:
            try:
                result = await crawler.arun(url=url, config=config)
                if result.success:
                    safe_filename = url.replace("https://", "").replace("/", "_").replace(":", "") + ".md"
                    output_path = os.path.join(output_dir, safe_filename)
                    
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(result.markdown)
                    
                    content_length = len(result.markdown.strip())
                    print(f"✅ 成功保存: {url}")
                    print(f"   文件: {output_path}")
                    print(f"   内容长度: {content_length} 字符")
                    print(f"   前100字符: {result.markdown[:100]}")
                    print()
                else:
                    print(f"❌ 爬取失败: {url} (错误: {result.error})")
            except Exception as e:
                print(f"⚠️ 异常错误: {url} → {str(e)}")

asyncio.run(test_fixed_crawl())

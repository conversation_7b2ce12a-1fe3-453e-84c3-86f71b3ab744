{"success": true, "results": [{"url": "https://www.yingdao.com/yddoc/rpa", "html": "\n.anticon {\n  display: inline-flex;\n  align-items: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n:where(.css-djtmh8) a{color:#1677ff;text-decoration:none;background-color:transparent;outline:none;cursor:pointer;transition:color 0.3s;-webkit-text-decoration-skip:objects;}:where(.css-djtmh8) a:hover{color:#69b1ff;}:where(.css-djtmh8) a:active{color:#0958d9;}:where(.css-djtmh8) a:active,:where(.css-djtmh8) a:hover{text-decoration:none;outline:0;}:where(.css-djtmh8) a:focus{text-decoration:none;outline:0;}:where(.css-djtmh8) a[disabled]{color:rgba(0, 0, 0, 0.25);cursor:not-allowed;}:where(.css-djtmh8)[class^=\"ant-input\"],:where(.css-djtmh8)[class*=\" ant-input\"]{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-djtmh8)[class^=\"ant-input\"]::before,:where(.css-djtmh8)[class*=\" ant-input\"]::before,:where(.css-djtmh8)[class^=\"ant-input\"]::after,:where(.css-djtmh8)[class*=\" ant-input\"]::after{box-sizing:border-box;}:where(.css-djtmh8)[class^=\"ant-input\"] [class^=\"ant-input\"],:where(.css-djtmh8)[class*=\" ant-input\"] [class^=\"ant-input\"],:where(.css-djtmh8)[class^=\"ant-input\"] [class*=\" ant-input\"],:where(.css-djtmh8)[class*=\" ant-input\"] [class*=\" ant-input\"]{box-sizing:border-box;}:where(.css-djtmh8)[class^=\"ant-input\"] [class^=\"ant-input\"]::before,:where(.css-djtmh8)[class*=\" ant-input\"] [class^=\"ant-input\"]::before,:where(.css-djtmh8)[class^=\"ant-input\"] [class*=\" ant-input\"]::before,:where(.css-djtmh8)[class*=\" ant-input\"] [class*=\" ant-input\"]::before,:where(.css-djtmh8)[class^=\"ant-input\"] [class^=\"ant-input\"]::after,:where(.css-djtmh8)[class*=\" ant-input\"] [class^=\"ant-input\"]::after,:where(.css-djtmh8)[class^=\"ant-input\"] [class*=\" ant-input\"]::after,:where(.css-djtmh8)[class*=\" ant-input\"] [class*=\" ant-input\"]::after{box-sizing:border-box;}:where(.css-djtmh8).ant-input{box-sizing:border-box;margin:0;padding:4px 11px;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';position:relative;display:inline-block;width:100%;min-width:0;border-radius:6px;transition:all 0.2s;}:where(.css-djtmh8).ant-input::-moz-placeholder{opacity:1;}:where(.css-djtmh8).ant-input::placeholder{color:rgba(0, 0, 0, 0.25);user-select:none;}:where(.css-djtmh8).ant-input:placeholder-shown{text-overflow:ellipsis;}textarea:where(.css-djtmh8).ant-input{max-width:100%;height:auto;min-height:32px;line-height:1.5714285714285714;vertical-align:bottom;transition:all 0.3s,height 0s;resize:vertical;}:where(.css-djtmh8).ant-input-lg{padding:7px 11px;font-size:16px;line-height:1.5;border-radius:8px;}:where(.css-djtmh8).ant-input-sm{padding:0px 7px;font-size:14px;border-radius:4px;}:where(.css-djtmh8).ant-input-rtl{direction:rtl;}:where(.css-djtmh8).ant-input-textarea-rtl{direction:rtl;}:where(.css-djtmh8).ant-input-outlined{background:#ffffff;border-width:1px;border-style:solid;border-color:#d9d9d9;}:where(.css-djtmh8).ant-input-outlined:hover{border-color:#4096ff;background-color:#ffffff;}:where(.css-djtmh8).ant-input-outlined:focus,:where(.css-djtmh8).ant-input-outlined:focus-within{border-color:#1677ff;box-shadow:0 0 0 2px rgba(5, 145, 255, 0.1);outline:0;background-color:#ffffff;}:where(.css-djtmh8).ant-input-outlined.ant-input-disabled,:where(.css-djtmh8).ant-input-outlined[disabled]{color:rgba(0, 0, 0, 0.25);background-color:rgba(0, 0, 0, 0.04);border-color:#d9d9d9;box-shadow:none;cursor:not-allowed;opacity:1;}:where(.css-djtmh8).ant-input-outlined.ant-input-disabled input[disabled],:where(.css-djtmh8).ant-input-outlined[disabled] input[disabled]{cursor:not-allowed;}:where(.css-djtmh8).ant-input-outlined.ant-input-disabled:hover:not([disabled]),:where(.css-djtmh8).ant-input-outlined[disabled]:hover:not([disabled]){border-color:#d9d9d9;background-color:rgba(0, 0, 0, 0.04);}:where(.css-djtmh8).ant-input-outlined.ant-input-status-error:not(.ant-input-disabled){background:#ffffff;border-width:1px;border-style:solid;border-color:#ff4d4f;}:where(.css-djtmh8).ant-input-outlined.ant-input-status-error:not(.ant-input-disabled):hover{border-color:#ffa39e;background-color:#ffffff;}:where(.css-djtmh8).ant-input-outlined.ant-input-status-error:not(.ant-input-disabled):focus,:where(.css-djtmh8).ant-input-outlined.ant-input-status-error:not(.ant-input-disabled):focus-within{border-color:#ff4d4f;box-shadow:0 0 0 2px rgba(255, 38, 5, 0.06);outline:0;background-color:#ffffff;}:where(.css-djtmh8).ant-input-outlined.ant-input-status-error:not(.ant-input-disabled) .ant-input-prefix,:where(.css-djtmh8).ant-input-outlined.ant-input-status-error:not(.ant-input-disabled) .ant-input-suffix{color:#ff4d4f;}:where(.css-djtmh8).ant-input-outlined.ant-input-status-warning:not(.ant-input-disabled){background:#ffffff;border-width:1px;border-style:solid;border-color:#faad14;}:where(.css-djtmh8).ant-input-outlined.ant-input-status-warning:not(.ant-input-disabled):hover{border-color:#ffd666;background-color:#ffffff;}:where(.css-djtmh8).ant-input-outlined.ant-input-status-warning:not(.ant-input-disabled):focus,:where(.css-djtmh8).ant-input-outlined.ant-input-status-warning:not(.ant-input-disabled):focus-within{border-color:#faad14;box-shadow:0 0 0 2px rgba(255, 215, 5, 0.1);outline:0;background-color:#ffffff;}:where(.css-djtmh8).ant-input-outlined.ant-input-status-warning:not(.ant-input-disabled) .ant-input-prefix,:where(.css-djtmh8).ant-input-outlined.ant-input-status-warning:not(.ant-input-disabled) .ant-input-suffix{color:#faad14;}:where(.css-djtmh8).ant-input-filled{background:rgba(0, 0, 0, 0.04);border-width:1px;border-style:solid;border-color:transparent;}input:where(.css-djtmh8).ant-input-filled,:where(.css-djtmh8).ant-input-filled input,textarea:where(.css-djtmh8).ant-input-filled,:where(.css-djtmh8).ant-input-filled textarea{color:undefined;}:where(.css-djtmh8).ant-input-filled:hover{background:rgba(0, 0, 0, 0.06);}:where(.css-djtmh8).ant-input-filled:focus,:where(.css-djtmh8).ant-input-filled:focus-within{outline:0;border-color:#1677ff;background-color:#ffffff;}:where(.css-djtmh8).ant-input-filled.ant-input-disabled,:where(.css-djtmh8).ant-input-filled[disabled]{color:rgba(0, 0, 0, 0.25);background-color:rgba(0, 0, 0, 0.04);border-color:#d9d9d9;box-shadow:none;cursor:not-allowed;opacity:1;}:where(.css-djtmh8).ant-input-filled.ant-input-disabled input[disabled],:where(.css-djtmh8).ant-input-filled[disabled] input[disabled]{cursor:not-allowed;}:where(.css-djtmh8).ant-input-filled.ant-input-disabled:hover:not([disabled]),:where(.css-djtmh8).ant-input-filled[disabled]:hover:not([disabled]){border-color:#d9d9d9;background-color:rgba(0, 0, 0, 0.04);}:where(.css-djtmh8).ant-input-filled.ant-input-status-error:not(.ant-input-disabled){background:#fff2f0;border-width:1px;border-style:solid;border-color:transparent;}input:where(.css-djtmh8).ant-input-filled.ant-input-status-error:not(.ant-input-disabled),:where(.css-djtmh8).ant-input-filled.ant-input-status-error:not(.ant-input-disabled) input,textarea:where(.css-djtmh8).ant-input-filled.ant-input-status-error:not(.ant-input-disabled),:where(.css-djtmh8).ant-input-filled.ant-input-status-error:not(.ant-input-disabled) textarea{color:#ff4d4f;}:where(.css-djtmh8).ant-input-filled.ant-input-status-error:not(.ant-input-disabled):hover{background:#fff1f0;}:where(.css-djtmh8).ant-input-filled.ant-input-status-error:not(.ant-input-disabled):focus,:where(.css-djtmh8).ant-input-filled.ant-input-status-error:not(.ant-input-disabled):focus-within{outline:0;border-color:#ff4d4f;background-color:#ffffff;}:where(.css-djtmh8).ant-input-filled.ant-input-status-error:not(.ant-input-disabled) .ant-input-prefix,:where(.css-djtmh8).ant-input-filled.ant-input-status-error:not(.ant-input-disabled) .ant-input-suffix{color:#ff4d4f;}:where(.css-djtmh8).ant-input-filled.ant-input-status-warning:not(.ant-input-disabled){background:#fffbe6;border-width:1px;border-style:solid;border-color:transparent;}input:where(.css-djtmh8).ant-input-filled.ant-input-status-warning:not(.ant-input-disabled),:where(.css-djtmh8).ant-input-filled.ant-input-status-warning:not(.ant-input-disabled) input,textarea:where(.css-djtmh8).ant-input-filled.ant-input-status-warning:not(.ant-input-disabled),:where(.css-djtmh8).ant-input-filled.ant-input-status-warning:not(.ant-input-disabled) textarea{color:#faad14;}:where(.css-djtmh8).ant-input-filled.ant-input-status-warning:not(.ant-input-disabled):hover{background:#fff1b8;}:where(.css-djtmh8).ant-input-filled.ant-input-status-warning:not(.ant-input-disabled):focus,:where(.css-djtmh8).ant-input-filled.ant-input-status-warning:not(.ant-input-disabled):focus-within{outline:0;border-color:#faad14;background-color:#ffffff;}:where(.css-djtmh8).ant-input-filled.ant-input-status-warning:not(.ant-input-disabled) .ant-input-prefix,:where(.css-djtmh8).ant-input-filled.ant-input-status-warning:not(.ant-input-disabled) .ant-input-suffix{color:#faad14;}:where(.css-djtmh8).ant-input-borderless{background:transparent;border:none;}:where(.css-djtmh8).ant-input-borderless:focus,:where(.css-djtmh8).ant-input-borderless:focus-within{outline:none;}:where(.css-djtmh8).ant-input-borderless.ant-input-disabled,:where(.css-djtmh8).ant-input-borderless[disabled]{color:rgba(0, 0, 0, 0.25);}:where(.css-djtmh8).ant-input[type=\"color\"]{height:32px;}:where(.css-djtmh8).ant-input[type=\"color\"].ant-input-lg{height:40px;}:where(.css-djtmh8).ant-input[type=\"color\"].ant-input-sm{height:24px;padding-top:3px;padding-bottom:3px;}:where(.css-djtmh8).ant-input[type=\"search\"]::-webkit-search-cancel-button,:where(.css-djtmh8).ant-input[type=\"search\"]::-webkit-search-decoration{-webkit-appearance:none;}:where(.css-djtmh8).ant-input-textarea{position:relative;}:where(.css-djtmh8).ant-input-textarea-show-count >.ant-input{height:100%;}:where(.css-djtmh8).ant-input-textarea-show-count .ant-input-data-count{position:absolute;bottom:-22px;inset-inline-end:0;color:rgba(0, 0, 0, 0.45);white-space:nowrap;pointer-events:none;}:where(.css-djtmh8).ant-input-textarea-allow-clear >.ant-input{padding-inline-end:24px;}:where(.css-djtmh8).ant-input-textarea-affix-wrapper.ant-input-textarea-has-feedback .ant-input{padding-inline-end:24px;}:where(.css-djtmh8).ant-input-textarea-affix-wrapper.ant-input-affix-wrapper{padding:0;}:where(.css-djtmh8).ant-input-textarea-affix-wrapper.ant-input-affix-wrapper >textarea.ant-input{font-size:inherit;border:none;outline:none;background:transparent;}:where(.css-djtmh8).ant-input-textarea-affix-wrapper.ant-input-affix-wrapper >textarea.ant-input:focus{box-shadow:none!important;}:where(.css-djtmh8).ant-input-textarea-affix-wrapper.ant-input-affix-wrapper .ant-input-suffix{margin:0;}:where(.css-djtmh8).ant-input-textarea-affix-wrapper.ant-input-affix-wrapper .ant-input-suffix >*:not(:last-child){margin-inline:0;}:where(.css-djtmh8).ant-input-textarea-affix-wrapper.ant-input-affix-wrapper .ant-input-suffix .ant-input-clear-icon{position:absolute;inset-inline-end:8px;inset-block-start:8px;}:where(.css-djtmh8).ant-input-textarea-affix-wrapper.ant-input-affix-wrapper .ant-input-suffix .ant-input-textarea-suffix{position:absolute;top:0;inset-inline-end:11px;bottom:0;z-index:1;display:inline-flex;align-items:center;margin:auto;pointer-events:none;}:where(.css-djtmh8).ant-input-affix-wrapper{position:relative;display:inline-flex;width:100%;min-width:0;padding:4px 11px;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1.5714285714285714;border-radius:6px;transition:all 0.2s;}:where(.css-djtmh8).ant-input-affix-wrapper::-moz-placeholder{opacity:1;}:where(.css-djtmh8).ant-input-affix-wrapper::placeholder{color:rgba(0, 0, 0, 0.25);user-select:none;}:where(.css-djtmh8).ant-input-affix-wrapper:placeholder-shown{text-overflow:ellipsis;}textarea:where(.css-djtmh8).ant-input-affix-wrapper{max-width:100%;height:auto;min-height:32px;line-height:1.5714285714285714;vertical-align:bottom;transition:all 0.3s,height 0s;resize:vertical;}:where(.css-djtmh8).ant-input-affix-wrapper-lg{padding:7px 11px;font-size:16px;line-height:1.5;border-radius:8px;}:where(.css-djtmh8).ant-input-affix-wrapper-sm{padding:0px 7px;font-size:14px;border-radius:4px;}:where(.css-djtmh8).ant-input-affix-wrapper-rtl{direction:rtl;}:where(.css-djtmh8).ant-input-affix-wrapper-textarea-rtl{direction:rtl;}:where(.css-djtmh8).ant-input-affix-wrapper:not(.ant-input-disabled):hover{z-index:1;}.ant-input-search-with-button :where(.css-djtmh8).ant-input-affix-wrapper:not(.ant-input-disabled):hover{z-index:0;}:where(.css-djtmh8).ant-input-affix-wrapper-focused,:where(.css-djtmh8).ant-input-affix-wrapper:focus{z-index:1;}:where(.css-djtmh8).ant-input-affix-wrapper >input.ant-input{padding:0;font-size:inherit;border:none;border-radius:0;outline:none;background:transparent;color:inherit;}:where(.css-djtmh8).ant-input-affix-wrapper >input.ant-input::-ms-reveal{display:none;}:where(.css-djtmh8).ant-input-affix-wrapper >input.ant-input:focus{box-shadow:none!important;}:where(.css-djtmh8).ant-input-affix-wrapper::before{display:inline-block;width:0;visibility:hidden;content:\"\\a0\";}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-prefix,:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-suffix{display:flex;flex:none;align-items:center;}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-prefix >*:not(:last-child),:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-suffix >*:not(:last-child){margin-inline-end:8px;}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-show-count-suffix{color:rgba(0, 0, 0, 0.45);}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-show-count-has-suffix{margin-inline-end:4px;}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-prefix{margin-inline-end:4px;}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-suffix{margin-inline-start:4px;}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-clear-icon{margin:0;color:rgba(0, 0, 0, 0.25);font-size:12px;vertical-align:-1px;cursor:pointer;transition:color 0.3s;}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-clear-icon:hover{color:rgba(0, 0, 0, 0.45);}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-clear-icon:active{color:rgba(0, 0, 0, 0.88);}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-clear-icon-hidden{visibility:hidden;}:where(.css-djtmh8).ant-input-affix-wrapper .ant-input-clear-icon-has-suffix{margin:0 4px;}:where(.css-djtmh8).ant-input-affix-wrapper .anticon.ant-input-password-icon{color:rgba(0, 0, 0, 0.45);cursor:pointer;transition:all 0.3s;}:where(.css-djtmh8).ant-input-affix-wrapper .anticon.ant-input-password-icon:hover{color:rgba(0, 0, 0, 0.88);}:where(.css-djtmh8).ant-input-group{box-sizing:border-box;margin:0;padding:0;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';position:relative;display:table;width:100%;border-collapse:separate;border-spacing:0;}:where(.css-djtmh8).ant-input-group[class*='col-']{padding-inline-end:8px;}:where(.css-djtmh8).ant-input-group[class*='col-']:last-child{padding-inline-end:0;}:where(.css-djtmh8).ant-input-group-lg .ant-input,:where(.css-djtmh8).ant-input-group-lg>.ant-input-group-addon{padding:7px 11px;font-size:16px;line-height:1.5;border-radius:8px;}:where(.css-djtmh8).ant-input-group-sm .ant-input,:where(.css-djtmh8).ant-input-group-sm>.ant-input-group-addon{padding:0px 7px;font-size:14px;border-radius:4px;}:where(.css-djtmh8).ant-input-group-lg .ant-select-single .ant-select-selector{height:40px;}:where(.css-djtmh8).ant-input-group-sm .ant-select-single .ant-select-selector{height:24px;}:where(.css-djtmh8).ant-input-group >.ant-input{display:table-cell;}:where(.css-djtmh8).ant-input-group >.ant-input:not(:first-child):not(:last-child){border-radius:0;}:where(.css-djtmh8).ant-input-group .ant-input-group-addon,:where(.css-djtmh8).ant-input-group .ant-input-group-wrap{display:table-cell;width:1px;white-space:nowrap;vertical-align:middle;}:where(.css-djtmh8).ant-input-group .ant-input-group-addon:not(:first-child):not(:last-child),:where(.css-djtmh8).ant-input-group .ant-input-group-wrap:not(:first-child):not(:last-child){border-radius:0;}:where(.css-djtmh8).ant-input-group .ant-input-group-wrap>*{display:block!important;}:where(.css-djtmh8).ant-input-group .ant-input-group-addon{position:relative;padding:0 11px;color:rgba(0, 0, 0, 0.88);font-weight:normal;font-size:14px;text-align:center;border-radius:6px;transition:all 0.3s;line-height:1;}:where(.css-djtmh8).ant-input-group .ant-input-group-addon .ant-select{margin:-5px -11px;}:where(.css-djtmh8).ant-input-group .ant-input-group-addon .ant-select.ant-select-single:not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{background-color:inherit;border:1px solid transparent;box-shadow:none;}:where(.css-djtmh8).ant-input-group .ant-input-group-addon .ant-select-open .ant-select-selector,:where(.css-djtmh8).ant-input-group .ant-input-group-addon .ant-select-focused .ant-select-selector{color:#1677ff;}:where(.css-djtmh8).ant-input-group .ant-input-group-addon .ant-cascader-picker{margin:-9px -11px;background-color:transparent;}:where(.css-djtmh8).ant-input-group .ant-input-group-addon .ant-cascader-picker .ant-cascader-input{text-align:start;border:0;box-shadow:none;}:where(.css-djtmh8).ant-input-group .ant-input{width:100%;margin-bottom:0;text-align:inherit;}:where(.css-djtmh8).ant-input-group .ant-input:focus{z-index:1;border-inline-end-width:1px;}:where(.css-djtmh8).ant-input-group .ant-input:hover{z-index:1;border-inline-end-width:1px;}.ant-input-search-with-button :where(.css-djtmh8).ant-input-group .ant-input:hover{z-index:0;}:where(.css-djtmh8).ant-input-group >.ant-input:first-child,:where(.css-djtmh8).ant-input-group .ant-input-group-addon:first-child{border-start-end-radius:0;border-end-end-radius:0;}:where(.css-djtmh8).ant-input-group >.ant-input:first-child .ant-select .ant-select-selector,:where(.css-djtmh8).ant-input-group .ant-input-group-addon:first-child .ant-select .ant-select-selector{border-start-end-radius:0;border-end-end-radius:0;}:where(.css-djtmh8).ant-input-group >.ant-input-affix-wrapper:not(:first-child) .ant-input{border-start-start-radius:0;border-end-start-radius:0;}:where(.css-djtmh8).ant-input-group >.ant-input-affix-wrapper:not(:last-child) .ant-input{border-start-end-radius:0;border-end-end-radius:0;}:where(.css-djtmh8).ant-input-group >.ant-input:last-child,:where(.css-djtmh8).ant-input-group .ant-input-group-addon:last-child{border-start-start-radius:0;border-end-start-radius:0;}:where(.css-djtmh8).ant-input-group >.ant-input:last-child .ant-select .ant-select-selector,:where(.css-djtmh8).ant-input-group .ant-input-group-addon:last-child .ant-select .ant-select-selector{border-start-start-radius:0;border-end-start-radius:0;}:where(.css-djtmh8).ant-input-group .ant-input-affix-wrapper:not(:last-child){border-start-end-radius:0;border-end-end-radius:0;}.ant-input-search :where(.css-djtmh8).ant-input-group .ant-input-affix-wrapper:not(:last-child){border-start-start-radius:6px;border-end-start-radius:6px;}:where(.css-djtmh8).ant-input-group .ant-input-affix-wrapper:not(:first-child),.ant-input-search :where(.css-djtmh8).ant-input-group .ant-input-affix-wrapper:not(:first-child){border-start-start-radius:0;border-end-start-radius:0;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact{display:block;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact::before{display:table;content:\"\";}:where(.css-djtmh8).ant-input-group.ant-input-group-compact::after{display:table;clear:both;content:\"\";}:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input-group-addon:not(:first-child):not(:last-child),:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input-group-wrap:not(:first-child):not(:last-child),:where(.css-djtmh8).ant-input-group.ant-input-group-compact >.ant-input:not(:first-child):not(:last-child){border-inline-end-width:1px;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input-group-addon:not(:first-child):not(:last-child):hover,:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input-group-wrap:not(:first-child):not(:last-child):hover,:where(.css-djtmh8).ant-input-group.ant-input-group-compact >.ant-input:not(:first-child):not(:last-child):hover{z-index:1;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input-group-addon:not(:first-child):not(:last-child):focus,:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input-group-wrap:not(:first-child):not(:last-child):focus,:where(.css-djtmh8).ant-input-group.ant-input-group-compact >.ant-input:not(:first-child):not(:last-child):focus{z-index:1;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>*{display:inline-block;float:none;vertical-align:top;border-radius:0;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-input-affix-wrapper,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-input-number-affix-wrapper,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-picker-range{display:inline-flex;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>*:not(:last-child){margin-inline-end:-1px;border-inline-end-width:1px;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input{float:none;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select>.ant-select-selector,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-cascader-picker .ant-input,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-input-group-wrapper .ant-input{border-inline-end-width:1px;border-radius:0;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select>.ant-select-selector:hover,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input:hover,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-cascader-picker .ant-input:hover,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-input-group-wrapper .ant-input:hover{z-index:1;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select>.ant-select-selector:focus,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input:focus,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-cascader-picker .ant-input:focus,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-input-group-wrapper .ant-input:focus{z-index:1;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select-focused{z-index:1;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select>.ant-select-arrow{z-index:1;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>*:first-child,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select:first-child>.ant-select-selector,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select-auto-complete:first-child .ant-input,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-cascader-picker:first-child .ant-input{border-start-start-radius:6px;border-end-start-radius:6px;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>*:last-child,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select:last-child>.ant-select-selector,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-cascader-picker:last-child .ant-input,:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-cascader-picker-focused:last-child .ant-input{border-inline-end-width:1px;border-start-end-radius:6px;border-end-end-radius:6px;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input{vertical-align:top;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input-group-wrapper+.ant-input-group-wrapper{margin-inline-start:-1px;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input-group-wrapper+.ant-input-group-wrapper .ant-input-affix-wrapper{border-radius:0;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input-group-wrapper:not(:last-child).ant-input-search>.ant-input-group>.ant-input-group-addon>.ant-input-search-button{border-radius:0;}:where(.css-djtmh8).ant-input-group.ant-input-group-compact .ant-input-group-wrapper:not(:last-child).ant-input-search>.ant-input-group>.ant-input{border-start-start-radius:6px;border-start-end-radius:0;border-end-end-radius:0;border-end-start-radius:6px;}:where(.css-djtmh8).ant-input-group-rtl{direction:rtl;}:where(.css-djtmh8).ant-input-group-wrapper{display:inline-block;width:100%;text-align:start;vertical-align:top;}:where(.css-djtmh8).ant-input-group-wrapper-rtl{direction:rtl;}:where(.css-djtmh8).ant-input-group-wrapper-lg .ant-input-group-addon{border-radius:8px;font-size:16px;}:where(.css-djtmh8).ant-input-group-wrapper-sm .ant-input-group-addon{border-radius:4px;}:where(.css-djtmh8).ant-input-group-wrapper-outlined .ant-input-group-addon{background:rgba(0, 0, 0, 0.02);border:1px solid #d9d9d9;}:where(.css-djtmh8).ant-input-group-wrapper-outlined .ant-input-group-addon:first-child{border-inline-end:0;}:where(.css-djtmh8).ant-input-group-wrapper-outlined .ant-input-group-addon:last-child{border-inline-start:0;}:where(.css-djtmh8).ant-input-group-wrapper-outlined.ant-input-group-wrapper-status-error .ant-input-group-addon{border-color:#ff4d4f;color:#ff4d4f;}:where(.css-djtmh8).ant-input-group-wrapper-outlined.ant-input-group-wrapper-status-warning .ant-input-group-addon{border-color:#faad14;color:#faad14;}:where(.css-djtmh8).ant-input-group-wrapper-outlined.ant-input-group-wrapper-disabled .ant-input-group-addon{color:rgba(0, 0, 0, 0.25);background-color:rgba(0, 0, 0, 0.04);border-color:#d9d9d9;box-shadow:none;cursor:not-allowed;opacity:1;}:where(.css-djtmh8).ant-input-group-wrapper-outlined.ant-input-group-wrapper-disabled .ant-input-group-addon input[disabled]{cursor:not-allowed;}:where(.css-djtmh8).ant-input-group-wrapper-outlined.ant-input-group-wrapper-disabled .ant-input-group-addon:hover:not([disabled]){border-color:#d9d9d9;background-color:rgba(0, 0, 0, 0.04);}:where(.css-djtmh8).ant-input-group-wrapper-filled .ant-input-group-addon{background:rgba(0, 0, 0, 0.04);}:where(.css-djtmh8).ant-input-group-wrapper-filled .ant-input-group .ant-input-filled:not(:focus):not(:focus-within):not(:first-child){border-inline-start:1px solid rgba(5, 5, 5, 0.06);}:where(.css-djtmh8).ant-input-group-wrapper-filled .ant-input-group .ant-input-filled:not(:focus):not(:focus-within):not(:last-child){border-inline-end:1px solid rgba(5, 5, 5, 0.06);}:where(.css-djtmh8).ant-input-group-wrapper-filled.ant-input-group-wrapper-status-error .ant-input-group-addon{background:#fff2f0;color:#ff4d4f;}:where(.css-djtmh8).ant-input-group-wrapper-filled.ant-input-group-wrapper-status-warning .ant-input-group-addon{background:#fffbe6;color:#faad14;}:where(.css-djtmh8).ant-input-group-wrapper-filled.ant-input-group-wrapper-disabled .ant-input-group-addon{background:rgba(0, 0, 0, 0.04);color:rgba(0, 0, 0, 0.25);}:where(.css-djtmh8).ant-input-group-wrapper-filled.ant-input-group-wrapper-disabled .ant-input-group-addon:first-child{border-inline-start:1px solid #d9d9d9;border-top:1px solid #d9d9d9;border-bottom:1px solid #d9d9d9;}:where(.css-djtmh8).ant-input-group-wrapper-filled.ant-input-group-wrapper-disabled .ant-input-group-addon:last-child{border-inline-end:1px solid #d9d9d9;border-top:1px solid #d9d9d9;border-bottom:1px solid #d9d9d9;}:where(.css-djtmh8).ant-input-group-wrapper:not(.ant-input-compact-first-item):not(.ant-input-compact-last-item).ant-input-compact-item .ant-input,:where(.css-djtmh8).ant-input-group-wrapper:not(.ant-input-compact-first-item):not(.ant-input-compact-last-item).ant-input-compact-item .ant-input-group-addon{border-radius:0;}:where(.css-djtmh8).ant-input-group-wrapper:not(.ant-input-compact-last-item).ant-input-compact-first-item .ant-input,:where(.css-djtmh8).ant-input-group-wrapper:not(.ant-input-compact-last-item).ant-input-compact-first-item .ant-input-group-addon{border-start-end-radius:0;border-end-end-radius:0;}:where(.css-djtmh8).ant-input-group-wrapper:not(.ant-input-compact-first-item).ant-input-compact-last-item .ant-input,:where(.css-djtmh8).ant-input-group-wrapper:not(.ant-input-compact-first-item).ant-input-compact-last-item .ant-input-group-addon{border-start-start-radius:0;border-end-start-radius:0;}:where(.css-djtmh8).ant-input-group-wrapper:not(.ant-input-compact-last-item).ant-input-compact-item .ant-input-affix-wrapper{border-start-end-radius:0;border-end-end-radius:0;}:where(.css-djtmh8).ant-input-search .ant-input:hover,:where(.css-djtmh8).ant-input-search .ant-input:focus{border-color:#4096ff;}:where(.css-djtmh8).ant-input-search .ant-input:hover +.ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary),:where(.css-djtmh8).ant-input-search .ant-input:focus +.ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary){border-inline-start-color:#4096ff;}:where(.css-djtmh8).ant-input-search .ant-input-affix-wrapper{border-radius:0;}:where(.css-djtmh8).ant-input-search .ant-input-lg{line-height:1.4998;}:where(.css-djtmh8).ant-input-search >.ant-input-group >.ant-input-group-addon:last-child{inset-inline-start:-1px;padding:0;border:0;}:where(.css-djtmh8).ant-input-search >.ant-input-group >.ant-input-group-addon:last-child .ant-input-search-button{margin-inline-end:-1px;padding-top:0;padding-bottom:0;border-start-start-radius:0;border-start-end-radius:6px;border-end-end-radius:6px;border-end-start-radius:0;box-shadow:none;}:where(.css-djtmh8).ant-input-search >.ant-input-group >.ant-input-group-addon:last-child .ant-input-search-button:not(.ant-btn-primary){color:rgba(0, 0, 0, 0.45);}:where(.css-djtmh8).ant-input-search >.ant-input-group >.ant-input-group-addon:last-child .ant-input-search-button:not(.ant-btn-primary):hover{color:#4096ff;}:where(.css-djtmh8).ant-input-search >.ant-input-group >.ant-input-group-addon:last-child .ant-input-search-button:not(.ant-btn-primary):active{color:#0958d9;}:where(.css-djtmh8).ant-input-search >.ant-input-group >.ant-input-group-addon:last-child .ant-input-search-button:not(.ant-btn-primary).ant-btn-loading::before{inset-inline-start:0;inset-inline-end:0;inset-block-start:0;inset-block-end:0;}:where(.css-djtmh8).ant-input-search .ant-input-search-button{height:32px;}:where(.css-djtmh8).ant-input-search .ant-input-search-button:hover,:where(.css-djtmh8).ant-input-search .ant-input-search-button:focus{z-index:1;}:where(.css-djtmh8).ant-input-search-large .ant-input-search-button{height:40px;}:where(.css-djtmh8).ant-input-search-small .ant-input-search-button{height:24px;}:where(.css-djtmh8).ant-input-search-rtl{direction:rtl;}:where(.css-djtmh8).ant-input-search.ant-input-compact-item:not(.ant-input-compact-last-item) .ant-input-group-addon .ant-input-search-button{margin-inline-end:-1px;border-radius:0;}:where(.css-djtmh8).ant-input-search.ant-input-compact-item:not(.ant-input-compact-first-item) .ant-input,:where(.css-djtmh8).ant-input-search.ant-input-compact-item:not(.ant-input-compact-first-item) .ant-input-affix-wrapper{border-radius:0;}:where(.css-djtmh8).ant-input-search.ant-input-compact-item >.ant-input-group-addon .ant-input-search-button:hover,:where(.css-djtmh8).ant-input-search.ant-input-compact-item >.ant-input:hover,:where(.css-djtmh8).ant-input-search.ant-input-compact-item .ant-input-affix-wrapper:hover,:where(.css-djtmh8).ant-input-search.ant-input-compact-item >.ant-input-group-addon .ant-input-search-button:focus,:where(.css-djtmh8).ant-input-search.ant-input-compact-item >.ant-input:focus,:where(.css-djtmh8).ant-input-search.ant-input-compact-item .ant-input-affix-wrapper:focus,:where(.css-djtmh8).ant-input-search.ant-input-compact-item >.ant-input-group-addon .ant-input-search-button:active,:where(.css-djtmh8).ant-input-search.ant-input-compact-item >.ant-input:active,:where(.css-djtmh8).ant-input-search.ant-input-compact-item .ant-input-affix-wrapper:active{z-index:2;}:where(.css-djtmh8).ant-input-search.ant-input-compact-item >.ant-input-affix-wrapper-focused{z-index:2;}:where(.css-djtmh8).ant-input-out-of-range,:where(.css-djtmh8).ant-input-out-of-range input,:where(.css-djtmh8).ant-input-out-of-range textarea,:where(.css-djtmh8).ant-input-out-of-range .ant-input-show-count-suffix,:where(.css-djtmh8).ant-input-out-of-range .ant-input-data-count{color:#ff4d4f;}:where(.css-djtmh8).ant-input-compact-item:not(.ant-input-compact-last-item){margin-inline-end:-1px;}:where(.css-djtmh8).ant-input-compact-item:hover,:where(.css-djtmh8).ant-input-compact-item:focus,:where(.css-djtmh8).ant-input-compact-item:active{z-index:2;}:where(.css-djtmh8).ant-input-compact-item[disabled]{z-index:0;}:where(.css-djtmh8).ant-input-compact-item:not(.ant-input-compact-first-item):not(.ant-input-compact-last-item){border-radius:0;}:where(.css-djtmh8).ant-input-compact-item:not(.ant-input-compact-last-item).ant-input-compact-first-item,:where(.css-djtmh8).ant-input-compact-item:not(.ant-input-compact-last-item).ant-input-compact-first-item.ant-input-sm,:where(.css-djtmh8).ant-input-compact-item:not(.ant-input-compact-last-item).ant-input-compact-first-item.ant-input-lg{border-start-end-radius:0;border-end-end-radius:0;}:where(.css-djtmh8).ant-input-compact-item:not(.ant-input-compact-first-item).ant-input-compact-last-item,:where(.css-djtmh8).ant-input-compact-item:not(.ant-input-compact-first-item).ant-input-compact-last-item.ant-input-sm,:where(.css-djtmh8).ant-input-compact-item:not(.ant-input-compact-first-item).ant-input-compact-last-item.ant-input-lg{border-start-start-radius:0;border-end-start-radius:0;}:where(.css-djtmh8)[class^=\"ant-modal\"],:where(.css-djtmh8)[class*=\" ant-modal\"]{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-djtmh8)[class^=\"ant-modal\"]::before,:where(.css-djtmh8)[class*=\" ant-modal\"]::before,:where(.css-djtmh8)[class^=\"ant-modal\"]::after,:where(.css-djtmh8)[class*=\" ant-modal\"]::after{box-sizing:border-box;}:where(.css-djtmh8)[class^=\"ant-modal\"] [class^=\"ant-modal\"],:where(.css-djtmh8)[class*=\" ant-modal\"] [class^=\"ant-modal\"],:where(.css-djtmh8)[class^=\"ant-modal\"] [class*=\" ant-modal\"],:where(.css-djtmh8)[class*=\" ant-modal\"] [class*=\" ant-modal\"]{box-sizing:border-box;}:where(.css-djtmh8)[class^=\"ant-modal\"] [class^=\"ant-modal\"]::before,:where(.css-djtmh8)[class*=\" ant-modal\"] [class^=\"ant-modal\"]::before,:where(.css-djtmh8)[class^=\"ant-modal\"] [class*=\" ant-modal\"]::before,:where(.css-djtmh8)[class*=\" ant-modal\"] [class*=\" ant-modal\"]::before,:where(.css-djtmh8)[class^=\"ant-modal\"] [class^=\"ant-modal\"]::after,:where(.css-djtmh8)[class*=\" ant-modal\"] [class^=\"ant-modal\"]::after,:where(.css-djtmh8)[class^=\"ant-modal\"] [class*=\" ant-modal\"]::after,:where(.css-djtmh8)[class*=\" ant-modal\"] [class*=\" ant-modal\"]::after{box-sizing:border-box;}:where(.css-djtmh8).ant-modal-root .ant-modal-wrap-rtl{direction:rtl;}:where(.css-djtmh8).ant-modal-root .ant-modal-centered{text-align:center;}:where(.css-djtmh8).ant-modal-root .ant-modal-centered::before{display:inline-block;width:0;height:100%;vertical-align:middle;content:\"\";}:where(.css-djtmh8).ant-modal-root .ant-modal-centered .ant-modal{top:0;display:inline-block;padding-bottom:0;text-align:start;vertical-align:middle;}@media (max-width: 767px){:where(.css-djtmh8).ant-modal-root .ant-modal{max-width:calc(100vw - 16px);margin:8px auto;}:where(.css-djtmh8).ant-modal-root .ant-modal-centered .ant-modal{flex:1;}}:where(.css-djtmh8).ant-modal{box-sizing:border-box;margin:0 auto;padding:0;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';pointer-events:none;position:relative;top:100px;width:auto;max-width:calc(100vw - 32px);padding-bottom:24px;}:where(.css-djtmh8).ant-modal .ant-modal-title{margin:0;color:rgba(0, 0, 0, 0.88);font-weight:600;font-size:16px;line-height:1.5;word-wrap:break-word;}:where(.css-djtmh8).ant-modal .ant-modal-content{position:relative;background-color:#ffffff;background-clip:padding-box;border:0;border-radius:8px;box-shadow:0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05);pointer-events:auto;padding:20px 24px;}:where(.css-djtmh8).ant-modal .ant-modal-close{position:absolute;top:12px;inset-inline-end:12px;z-index:1010;padding:0;color:rgba(0, 0, 0, 0.45);font-weight:600;line-height:1;text-decoration:none;background:transparent;border-radius:4px;width:32px;height:32px;border:0;outline:0;cursor:pointer;transition:color 0.2s,background-color 0.2s;}:where(.css-djtmh8).ant-modal .ant-modal-close-x{display:flex;font-size:16px;font-style:normal;line-height:32px;justify-content:center;text-transform:none;text-rendering:auto;}:where(.css-djtmh8).ant-modal .ant-modal-close:hover{color:rgba(0, 0, 0, 0.88);background-color:rgba(0, 0, 0, 0.06);text-decoration:none;}:where(.css-djtmh8).ant-modal .ant-modal-close:active{background-color:rgba(0, 0, 0, 0.15);}:where(.css-djtmh8).ant-modal .ant-modal-close:focus-visible{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-djtmh8).ant-modal .ant-modal-header{color:rgba(0, 0, 0, 0.88);background:#ffffff;border-radius:8px 8px 0 0;margin-bottom:8px;padding:0;border-bottom:none;}:where(.css-djtmh8).ant-modal .ant-modal-body{font-size:14px;line-height:1.5714285714285714;word-wrap:break-word;padding:0;}:where(.css-djtmh8).ant-modal .ant-modal-footer{text-align:end;background:transparent;margin-top:12px;padding:0;border-top:none;border-radius:0;}:where(.css-djtmh8).ant-modal .ant-modal-footer >.ant-btn+.ant-btn{margin-inline-start:8px;}:where(.css-djtmh8).ant-modal .ant-modal-open{overflow:hidden;}:where(.css-djtmh8).ant-modal-pure-panel{top:auto;padding:0;display:flex;flex-direction:column;}:where(.css-djtmh8).ant-modal-pure-panel .ant-modal-content,:where(.css-djtmh8).ant-modal-pure-panel .ant-modal-body,:where(.css-djtmh8).ant-modal-pure-panel .ant-modal-confirm-body-wrapper{display:flex;flex-direction:column;flex:auto;}:where(.css-djtmh8).ant-modal-pure-panel .ant-modal-confirm-body{margin-bottom:auto;}:where(.css-djtmh8).ant-modal-root .ant-modal-wrap-rtl{direction:rtl;}:where(.css-djtmh8).ant-modal-root .ant-modal-wrap-rtl .ant-modal-confirm-body{direction:rtl;}:where(.css-djtmh8).ant-modal-root .ant-modal.ant-zoom-enter,:where(.css-djtmh8).ant-modal-root .ant-modal.ant-zoom-appear{transform:none;opacity:0;animation-duration:0.3s;user-select:none;}:where(.css-djtmh8).ant-modal-root .ant-modal.ant-zoom-leave .ant-modal-content{pointer-events:none;}:where(.css-djtmh8).ant-modal-root .ant-modal-mask{position:fixed;inset:0;z-index:1000;height:100%;background-color:rgba(0, 0, 0, 0.45);pointer-events:none;}:where(.css-djtmh8).ant-modal-root .ant-modal-mask .ant-modal-hidden{display:none;}:where(.css-djtmh8).ant-modal-root .ant-modal-wrap{position:fixed;inset:0;z-index:1000;overflow:auto;outline:0;-webkit-overflow-scrolling:touch;}:where(.css-djtmh8).ant-modal-root .ant-fade-enter,:where(.css-djtmh8).ant-modal-root .ant-fade-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-djtmh8).ant-modal-root .ant-fade-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-djtmh8).ant-modal-root .ant-fade-enter.ant-fade-enter-active,:where(.css-djtmh8).ant-modal-root .ant-fade-appear.ant-fade-appear-active{animation-name:css-djtmh8-antFadeIn;animation-play-state:running;}:where(.css-djtmh8).ant-modal-root .ant-fade-leave.ant-fade-leave-active{animation-name:css-djtmh8-antFadeOut;animation-play-state:running;pointer-events:none;}:where(.css-djtmh8).ant-modal-root .ant-fade-enter,:where(.css-djtmh8).ant-modal-root .ant-fade-appear{opacity:0;animation-timing-function:linear;}:where(.css-djtmh8).ant-modal-root .ant-fade-leave{animation-timing-function:linear;}:where(.css-djtmh8).ant-zoom-enter,:where(.css-djtmh8).ant-zoom-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-djtmh8).ant-zoom-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-djtmh8).ant-zoom-enter.ant-zoom-enter-active,:where(.css-djtmh8).ant-zoom-appear.ant-zoom-appear-active{animation-name:css-djtmh8-antZoomIn;animation-play-state:running;}:where(.css-djtmh8).ant-zoom-leave.ant-zoom-leave-active{animation-name:css-djtmh8-antZoomOut;animation-play-state:running;pointer-events:none;}:where(.css-djtmh8).ant-zoom-enter,:where(.css-djtmh8).ant-zoom-appear{transform:scale(0);opacity:0;animation-timing-function:cubic-bezier(0.08, 0.82, 0.17, 1);}:where(.css-djtmh8).ant-zoom-enter-prepare,:where(.css-djtmh8).ant-zoom-appear-prepare{transform:none;}:where(.css-djtmh8).ant-zoom-leave{animation-timing-function:cubic-bezier(0.78, 0.14, 0.15, 0.86);}@keyframes css-djtmh8-antFadeIn{0%{opacity:0;}100%{opacity:1;}}@keyframes css-djtmh8-antFadeOut{0%{opacity:1;}100%{opacity:0;}}@keyframes css-djtmh8-antZoomIn{0%{transform:scale(0.2);opacity:0;}100%{transform:scale(1);opacity:1;}}@keyframes css-djtmh8-antZoomOut{0%{transform:scale(1);}100%{transform:scale(0.2);opacity:0;}}:where(.css-djtmh8).ant-tree{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-djtmh8).ant-tree::before,:where(.css-djtmh8).ant-tree::after{box-sizing:border-box;}:where(.css-djtmh8).ant-tree [class^=\"ant-tree\"],:where(.css-djtmh8).ant-tree [class*=\" ant-tree\"]{box-sizing:border-box;}:where(.css-djtmh8).ant-tree [class^=\"ant-tree\"]::before,:where(.css-djtmh8).ant-tree [class*=\" ant-tree\"]::before,:where(.css-djtmh8).ant-tree [class^=\"ant-tree\"]::after,:where(.css-djtmh8).ant-tree [class*=\" ant-tree\"]::after{box-sizing:border-box;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-group{box-sizing:border-box;margin:0;padding:0;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';display:inline-flex;flex-wrap:wrap;column-gap:8px;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-group >.ant-row{flex:1;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-wrapper{box-sizing:border-box;margin:0;padding:0;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';display:inline-flex;align-items:baseline;cursor:pointer;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-wrapper:after{display:inline-block;width:0;overflow:hidden;content:'\\a0';}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-wrapper+.ant-tree-checkbox-wrapper{margin-inline-start:0;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-wrapper.ant-tree-checkbox-wrapper-in-form-item input[type=\"checkbox\"]{width:14px;height:14px;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox{box-sizing:border-box;margin:0;padding:0;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';position:relative;white-space:nowrap;cursor:pointer;border-radius:4px;align-self:center;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox .ant-tree-checkbox-input{position:absolute;inset:0;z-index:1;cursor:pointer;opacity:0;margin:0;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox .ant-tree-checkbox-input:focus-visible+.ant-tree-checkbox-inner{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox .ant-tree-checkbox-inner{box-sizing:border-box;display:block;width:16px;height:16px;direction:ltr;background-color:#ffffff;border:1px solid #d9d9d9;border-radius:4px;border-collapse:separate;transition:all 0.3s;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox .ant-tree-checkbox-inner:after{box-sizing:border-box;position:absolute;top:50%;inset-inline-start:25%;display:table;width:5.7142857142857135px;height:9.142857142857142px;border:2px solid #fff;border-top:0;border-inline-start:0;transform:rotate(45deg) scale(0) translate(-50%,-50%);opacity:0;content:\"\";transition:all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6),opacity 0.1s;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox+span{padding-inline-start:8px;padding-inline-end:8px;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-wrapper:not(.ant-tree-checkbox-wrapper-disabled):hover .ant-tree-checkbox-inner,:where(.css-djtmh8).ant-tree .ant-tree-checkbox:not(.ant-tree-checkbox-disabled):hover .ant-tree-checkbox-inner{border-color:#1677ff;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-wrapper:not(.ant-tree-checkbox-wrapper-disabled):hover .ant-tree-checkbox-checked:not(.ant-tree-checkbox-disabled) .ant-tree-checkbox-inner{background-color:#4096ff;border-color:transparent;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-wrapper:not(.ant-tree-checkbox-wrapper-disabled):hover .ant-tree-checkbox-checked:not(.ant-tree-checkbox-disabled):after{border-color:#4096ff;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-checked .ant-tree-checkbox-inner{background-color:#1677ff;border-color:#1677ff;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-checked .ant-tree-checkbox-inner:after{opacity:1;transform:rotate(45deg) scale(1) translate(-50%,-50%);transition:all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-wrapper-checked:not(.ant-tree-checkbox-wrapper-disabled):hover .ant-tree-checkbox-inner,:where(.css-djtmh8).ant-tree .ant-tree-checkbox-checked:not(.ant-tree-checkbox-disabled):hover .ant-tree-checkbox-inner{background-color:#4096ff;border-color:transparent;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner{background-color:#ffffff;border-color:#d9d9d9;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner:after{top:50%;inset-inline-start:50%;width:8px;height:8px;background-color:#1677ff;border:0;transform:translate(-50%, -50%) scale(1);opacity:1;content:\"\";}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-wrapper-disabled{cursor:not-allowed;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-disabled,:where(.css-djtmh8).ant-tree .ant-tree-checkbox-disabled .ant-tree-checkbox-input{cursor:not-allowed;pointer-events:none;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-disabled .ant-tree-checkbox-inner{background:rgba(0, 0, 0, 0.04);border-color:#d9d9d9;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-disabled .ant-tree-checkbox-inner:after{border-color:rgba(0, 0, 0, 0.25);}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-disabled:after{display:none;}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-disabled+span{color:rgba(0, 0, 0, 0.25);}:where(.css-djtmh8).ant-tree .ant-tree-checkbox-disabled.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after{background:rgba(0, 0, 0, 0.25);}:where(.css-djtmh8).ant-tree{box-sizing:border-box;margin:0;padding:0;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';background:#ffffff;border-radius:6px;transition:background-color 0.3s;}:where(.css-djtmh8).ant-tree.ant-tree-rtl .ant-tree-switcher_close .ant-tree-switcher-icon svg{transform:rotate(90deg);}:where(.css-djtmh8).ant-tree-focused:not(:hover):not(.ant-tree-active-focused){outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-djtmh8).ant-tree .ant-tree-list-holder-inner{align-items:flex-start;}:where(.css-djtmh8).ant-tree.ant-tree-block-node .ant-tree-list-holder-inner{align-items:stretch;}:where(.css-djtmh8).ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-node-content-wrapper{flex:auto;}:where(.css-djtmh8).ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-treenode.dragging{position:relative;}:where(.css-djtmh8).ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-treenode.dragging:after{position:absolute;top:0;inset-inline-end:0;bottom:4px;inset-inline-start:0;border:1px solid #1677ff;opacity:0;animation-name:css-djtmh8-ant-tree-node-fx-do-not-use;animation-duration:0.3s;animation-play-state:running;animation-fill-mode:forwards;content:\"\";pointer-events:none;}:where(.css-djtmh8).ant-tree .ant-tree-treenode{display:flex;align-items:flex-start;padding:0 0 4px 0;outline:none;}:where(.css-djtmh8).ant-tree .ant-tree-treenode-rtl{direction:rtl;}:where(.css-djtmh8).ant-tree .ant-tree-treenode-disabled .ant-tree-node-content-wrapper{color:rgba(0, 0, 0, 0.25);cursor:not-allowed;}:where(.css-djtmh8).ant-tree .ant-tree-treenode-disabled .ant-tree-node-content-wrapper:hover{background:transparent;}:where(.css-djtmh8).ant-tree .ant-tree-treenode-active .ant-tree-node-content-wrapper{background:rgba(0, 0, 0, 0.04);}:where(.css-djtmh8).ant-tree .ant-tree-treenode:not(.ant-tree-treenode-disabled).filter-node .ant-tree-title{color:inherit;font-weight:500;}:where(.css-djtmh8).ant-tree .ant-tree-treenode-draggable{cursor:grab;}:where(.css-djtmh8).ant-tree .ant-tree-treenode-draggable .ant-tree-draggable-icon{flex-shrink:0;width:24px;line-height:24px;text-align:center;visibility:visible;opacity:0.2;transition:opacity 0.3s;}.ant-tree-treenode:hover :where(.css-djtmh8).ant-tree .ant-tree-treenode-draggable .ant-tree-draggable-icon{opacity:0.45;}:where(.css-djtmh8).ant-tree .ant-tree-treenode-draggable.ant-tree-treenode-disabled .ant-tree-draggable-icon{visibility:hidden;}:where(.css-djtmh8).ant-tree .ant-tree-indent{align-self:stretch;white-space:nowrap;user-select:none;}:where(.css-djtmh8).ant-tree .ant-tree-indent-unit{display:inline-block;width:24px;}:where(.css-djtmh8).ant-tree .ant-tree-draggable-icon{visibility:hidden;}:where(.css-djtmh8).ant-tree .ant-tree-switcher{position:relative;flex:none;align-self:stretch;width:24px;margin:0;line-height:24px;text-align:center;cursor:pointer;user-select:none;transition:all 0.3s;border-radius:6px;}:where(.css-djtmh8).ant-tree .ant-tree-switcher .ant-tree-switcher-icon{display:inline-block;font-size:10px;vertical-align:baseline;}:where(.css-djtmh8).ant-tree .ant-tree-switcher .ant-tree-switcher-icon svg{transition:transform 0.3s;}:where(.css-djtmh8).ant-tree .ant-tree-switcher-noop{cursor:unset;}:where(.css-djtmh8).ant-tree .ant-tree-switcher:not(.ant-tree-switcher-noop):hover{background-color:rgba(0, 0, 0, 0.06);}:where(.css-djtmh8).ant-tree .ant-tree-switcher_close .ant-tree-switcher-icon svg{transform:rotate(-90deg);}:where(.css-djtmh8).ant-tree .ant-tree-switcher-loading-icon{color:#1677ff;}:where(.css-djtmh8).ant-tree .ant-tree-switcher-leaf-line{position:relative;z-index:1;display:inline-block;width:100%;height:100%;}:where(.css-djtmh8).ant-tree .ant-tree-switcher-leaf-line:before{position:absolute;top:0;inset-inline-end:12px;bottom:-4px;margin-inline-start:-1px;border-inline-end:1px solid #d9d9d9;content:\"\";}:where(.css-djtmh8).ant-tree .ant-tree-switcher-leaf-line:after{position:absolute;width:9.600000000000001px;height:12px;border-bottom:1px solid #d9d9d9;content:\"\";}:where(.css-djtmh8).ant-tree .ant-tree-checkbox{top:initial;margin-inline-end:8px;align-self:flex-start;margin-top:4px;}:where(.css-djtmh8).ant-tree .ant-tree-node-content-wrapper,:where(.css-djtmh8).ant-tree .ant-tree-checkbox+span{position:relative;z-index:auto;min-height:24px;margin:0;padding:0 4px;color:inherit;line-height:24px;background:transparent;border-radius:6px;cursor:pointer;transition:all 0.2s,border 0s,line-height 0s,box-shadow 0s;}:where(.css-djtmh8).ant-tree .ant-tree-node-content-wrapper:hover,:where(.css-djtmh8).ant-tree .ant-tree-checkbox+span:hover{background-color:rgba(0, 0, 0, 0.04);}:where(.css-djtmh8).ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected,:where(.css-djtmh8).ant-tree .ant-tree-checkbox+span.ant-tree-node-selected{background-color:#e6f4ff;}:where(.css-djtmh8).ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle,:where(.css-djtmh8).ant-tree .ant-tree-checkbox+span .ant-tree-iconEle{display:inline-block;width:24px;height:24px;line-height:24px;text-align:center;vertical-align:top;}:where(.css-djtmh8).ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle:empty,:where(.css-djtmh8).ant-tree .ant-tree-checkbox+span .ant-tree-iconEle:empty{display:none;}:where(.css-djtmh8).ant-tree .ant-tree-unselectable .ant-tree-node-content-wrapper:hover{background-color:transparent;}:where(.css-djtmh8).ant-tree .ant-tree-node-content-wrapper{line-height:24px;user-select:none;}:where(.css-djtmh8).ant-tree .ant-tree-node-content-wrapper .ant-tree-drop-indicator{position:absolute;z-index:1;height:2px;background-color:#1677ff;border-radius:1px;pointer-events:none;}:where(.css-djtmh8).ant-tree .ant-tree-node-content-wrapper .ant-tree-drop-indicator:after{position:absolute;top:-3px;inset-inline-start:-6px;width:8px;height:8px;background-color:transparent;border:2px solid #1677ff;border-radius:50%;content:\"\";}:where(.css-djtmh8).ant-tree .ant-tree-treenode.drop-container >[draggable]{box-shadow:0 0 0 2px #1677ff;}:where(.css-djtmh8).ant-tree-show-line .ant-tree-indent-unit{position:relative;height:100%;}:where(.css-djtmh8).ant-tree-show-line .ant-tree-indent-unit:before{position:absolute;top:0;inset-inline-end:12px;bottom:-4px;border-inline-end:1px solid #d9d9d9;content:\"\";}:where(.css-djtmh8).ant-tree-show-line .ant-tree-indent-unit-end:before{display:none;}:where(.css-djtmh8).ant-tree-show-line .ant-tree-switcher{background:transparent;}:where(.css-djtmh8).ant-tree-show-line .ant-tree-switcher-line-icon{vertical-align:-0.15em;}:where(.css-djtmh8).ant-tree .ant-tree-treenode-leaf-last .ant-tree-switcher-leaf-line:before{top:auto!important;bottom:auto!important;height:12px!important;}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode{position:relative;}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode:before{position:absolute;top:0;inset-inline-end:0;bottom:4px;inset-inline-start:0;transition:background-color 0.2s;content:\"\";pointer-events:none;}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode:hover:before{background:rgba(0, 0, 0, 0.04);}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode >*{z-index:1;}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-switcher{transition:color 0.2s;}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper{border-radius:0;user-select:none;}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper:hover{background:transparent;}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected{color:#fff;background:transparent;}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode-selected:hover::before,:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode-selected::before{background:#1677ff;}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher{color:#fff;}:where(.css-djtmh8).ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper{color:#fff;background:transparent;}:where(.css-djtmh8).ant-tree .ant-motion-collapse-legacy{overflow:hidden;}:where(.css-djtmh8).ant-tree .ant-motion-collapse-legacy-active{transition:height 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1)!important;}:where(.css-djtmh8).ant-tree .ant-motion-collapse{overflow:hidden;transition:height 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1)!important;}@keyframes css-djtmh8-ant-tree-node-fx-do-not-use{0%{opacity:0;}100%{opacity:1;}}:where(.css-djtmh8).ant-breadcrumb{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-djtmh8).ant-breadcrumb::before,:where(.css-djtmh8).ant-breadcrumb::after{box-sizing:border-box;}:where(.css-djtmh8).ant-breadcrumb [class^=\"ant-breadcrumb\"],:where(.css-djtmh8).ant-breadcrumb [class*=\" ant-breadcrumb\"]{box-sizing:border-box;}:where(.css-djtmh8).ant-breadcrumb [class^=\"ant-breadcrumb\"]::before,:where(.css-djtmh8).ant-breadcrumb [class*=\" ant-breadcrumb\"]::before,:where(.css-djtmh8).ant-breadcrumb [class^=\"ant-breadcrumb\"]::after,:where(.css-djtmh8).ant-breadcrumb [class*=\" ant-breadcrumb\"]::after{box-sizing:border-box;}:where(.css-djtmh8).ant-breadcrumb{box-sizing:border-box;margin:0;padding:0;color:rgba(0, 0, 0, 0.45);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';}:where(.css-djtmh8).ant-breadcrumb .anticon{font-size:14px;}:where(.css-djtmh8).ant-breadcrumb ol{display:flex;flex-wrap:wrap;margin:0;padding:0;list-style:none;}:where(.css-djtmh8).ant-breadcrumb a{color:rgba(0, 0, 0, 0.45);transition:color 0.2s;padding:0 4px;border-radius:4px;height:22px;display:inline-block;margin-inline:-4px;}:where(.css-djtmh8).ant-breadcrumb a:hover{color:rgba(0, 0, 0, 0.88);background-color:rgba(0, 0, 0, 0.06);}:where(.css-djtmh8).ant-breadcrumb a:focus-visible{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-djtmh8).ant-breadcrumb li:last-child{color:rgba(0, 0, 0, 0.88);}:where(.css-djtmh8).ant-breadcrumb .ant-breadcrumb-separator{margin-inline:8px;color:rgba(0, 0, 0, 0.45);}:where(.css-djtmh8).ant-breadcrumb .ant-breadcrumb-link >.anticon+span,:where(.css-djtmh8).ant-breadcrumb .ant-breadcrumb-link >.anticon+a{margin-inline-start:4px;}:where(.css-djtmh8).ant-breadcrumb .ant-breadcrumb-overlay-link{border-radius:4px;height:22px;display:inline-block;padding:0 4px;margin-inline:-4px;}:where(.css-djtmh8).ant-breadcrumb .ant-breadcrumb-overlay-link >.anticon{margin-inline-start:4px;font-size:12px;}:where(.css-djtmh8).ant-breadcrumb .ant-breadcrumb-overlay-link:hover{color:rgba(0, 0, 0, 0.88);background-color:rgba(0, 0, 0, 0.06);}:where(.css-djtmh8).ant-breadcrumb .ant-breadcrumb-overlay-link:hover a{color:rgba(0, 0, 0, 0.88);}:where(.css-djtmh8).ant-breadcrumb .ant-breadcrumb-overlay-link a:hover{background-color:transparent;}:where(.css-djtmh8).ant-breadcrumb.ant-breadcrumb-rtl{direction:rtl;}:where(.css-djtmh8).ant-row{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-djtmh8).ant-row::before,:where(.css-djtmh8).ant-row::after{box-sizing:border-box;}:where(.css-djtmh8).ant-row [class^=\"ant-row\"],:where(.css-djtmh8).ant-row [class*=\" ant-row\"]{box-sizing:border-box;}:where(.css-djtmh8).ant-row [class^=\"ant-row\"]::before,:where(.css-djtmh8).ant-row [class*=\" ant-row\"]::before,:where(.css-djtmh8).ant-row [class^=\"ant-row\"]::after,:where(.css-djtmh8).ant-row [class*=\" ant-row\"]::after{box-sizing:border-box;}:where(.css-djtmh8).ant-row{display:flex;flex-flow:row wrap;min-width:0;}:where(.css-djtmh8).ant-row::before,:where(.css-djtmh8).ant-row::after{display:flex;}:where(.css-djtmh8).ant-row-no-wrap{flex-wrap:nowrap;}:where(.css-djtmh8).ant-row-start{justify-content:flex-start;}:where(.css-djtmh8).ant-row-center{justify-content:center;}:where(.css-djtmh8).ant-row-end{justify-content:flex-end;}:where(.css-djtmh8).ant-row-space-between{justify-content:space-between;}:where(.css-djtmh8).ant-row-space-around{justify-content:space-around;}:where(.css-djtmh8).ant-row-space-evenly{justify-content:space-evenly;}:where(.css-djtmh8).ant-row-top{align-items:flex-start;}:where(.css-djtmh8).ant-row-middle{align-items:center;}:where(.css-djtmh8).ant-row-bottom{align-items:flex-end;}.anticon{display:inline-flex;align-items:center;color:inherit;font-style:normal;line-height:0;text-align:center;text-transform:none;vertical-align:-0.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}.anticon >*{line-height:1;}.anticon svg{display:inline-block;}.anticon .anticon .anticon-icon{display:block;}影刀帮助中心\n                if (window && window.innerWidth > 800 && location.pathname.includes('yddoc/rpa')) {\n                    window.$$aaConfig = {\n                      url: \"https://assistant.yingdao.com/assistant/df0ad624-ba9e-466c-afdf-4bb510535880/sdk\",\n                      launcher:\n                        \"https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/ai_assistant/1721040169602/1bca9be3-2267-4bfb-80d6-c4aed68cc1ed\",\n                    };\n                  }\n              \n                if (window && window.innerWidth > 800 && location.pathname.includes('yddoc/ap')) {\n                    window.$$aaConfig = {\n                      uuid: \"ccc2cd3c-fc6a-426b-b4e1-996107dd5c90\",\n                      url: \"https://power.yingdao.com/assistant/ccc2cd3c-fc6a-426b-b4e1-996107dd5c90/share\",\n                      apiUrl: \"https://power-api.yingdao.com\",\n                    };\n                  }\n              \n                (function (c, l, a, r, i, t, y) {\n              c[a] =\n                c[a] ||\n                function () {\n                  (c[a].q = c[a].q || []).push(arguments);\n                };\n              t = l.createElement(r);\n              t.async = 1;\n              t.src = 'https://www.clarity.ms/tag/' + i;\n              y = l.getElementsByTagName(r)[0];\n              y.parentNode.insertBefore(t, y);\n            })(window, document, 'clarity', 'script', 'ch8uihcm3i');\n        html[dir=ltr],[data-sonner-toaster][dir=ltr]{--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}html[dir=rtl],[data-sonner-toaster][dir=rtl]{--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}[data-sonner-toaster][data-x-position=right]{right:max(var(--offset),env(safe-area-inset-right))}[data-sonner-toaster][data-x-position=left]{left:max(var(--offset),env(safe-area-inset-left))}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translate(-50%)}[data-sonner-toaster][data-y-position=top]{top:max(var(--offset),env(safe-area-inset-top))}[data-sonner-toaster][data-y-position=bottom]{bottom:max(var(--offset),env(safe-area-inset-bottom))}[data-sonner-toast]{--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;will-change:transform,opacity,height;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}[data-sonner-toast][data-y-position=top]{top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}[data-sonner-toast] [data-description]{font-weight:400;line-height:1.4;color:inherit}[data-sonner-toast] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast] [data-icon]>*{flex-shrink:0}[data-sonner-toast] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;transition:opacity .4s,box-shadow .2s}[data-sonner-toast] [data-button]:focus-visible{box-shadow:0 0 0 2px #0006}[data-sonner-toast] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast] [data-cancel]{color:var(--color);background:var(--border-color)}[data-sonner-toast] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;opacity:0;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]:focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}[data-sonner-toast] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast]:hover [data-close-button]{opacity:1}[data-sonner-toast]:focus [data-close-button]{opacity:1}[data-sonner-toast]:focus-within [data-close-button]{opacity:1}[data-sonner-toast]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]:before{content:\"\";position:absolute;left:0;right:0;height:100%}[data-sonner-toast][data-y-position=top][data-swiping=true]:before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]:before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]:before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast]:after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y: translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y: translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]:before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - 32px)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true] [data-sonner-toast][data-type=success],[data-rich-colors=true] [data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true] [data-sonner-toast][data-type=error],[data-rich-colors=true] [data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}\n\nmicro-app, micro-app-body { display: block; } \nmicro-app-head { display: none; }影刀RPA快速寻找答案快速开始影刀概述快速入门功能文档指令文档接口文档常见问题开放API管理文档专题文档解决方案首页>帮助文档趣味闯关，成为认证工程师快速入门 界面功能块介绍 应用搭建的一般流程 应用搭建的基本概念更多>>功能文档 元素捕获 应用运行相关 用户设置更多>>指令文档 条件判断 循环 等待更多>>接口文档 xbot.web xbot.win32 xbot.excel更多>>专题文档 编码版使用说明 自定义指令使用说明 移动端自动化搭建流程更多>>常见问题 影刀新手 FAQ 未找到元素 网页自动化常见问题更多>>管理文档 企业控制台 企业账号功能 调度管理更多>>解决方案 网页弹窗处理 如何调试影刀应用 网页懒加载场景和解决方案更多>>问题没有解决?去社区提问{\"props\":{\"pageProps\":{\"referrer\":\"\"},\"__N_SSP\":true},\"page\":\"/[productCode]\",\"query\":{\"productCode\":\"rpa\"},\"buildId\":\"RBUIhmoVCRdIYHlD79AI7\",\"assetPrefix\":\"/yddoc\",\"isFallback\":false,\"gssp\":true,\"scriptLoader\":[]}", "success": true, "cleaned_html": "影刀RPA快速寻找答案快速开始影刀概述快速入门功能文档指令文档接口文档常见问题开放API管理文档专题文档解决方案首页>帮助文档趣味闯关，成为认证工程师快速入门 界面功能块介绍 应用搭建的一般流程 应用搭建的基本概念更多>>功能文档 元素捕获 应用运行相关 用户设置更多>>指令文档 条件判断 循环 等待更多>>接口文档 xbot.web xbot.win32 xbot.excel更多>>专题文档 编码版使用说明 自定义指令使用说明 移动端自动化搭建流程更多>>常见问题 影刀新手 FAQ 未找到元素 网页自动化常见问题更多>>管理文档 企业控制台 企业账号功能 调度管理更多>>解决方案 网页弹窗处理 如何调试影刀应用 网页懒加载场景和解决方案更多>>问题没有解决?去社区提问", "media": {"images": [{"src": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/image/20220401093620/4cd91c34e675f59671fc16603597b5ec.png", "data": "", "alt": "", "desc": "趣味闯关，成为认证工程师", "score": 3, "type": "image", "group_id": 10, "format": "png", "width": null}], "videos": [], "audios": []}, "links": {"internal": [{"href": "https://www.yingdao.com/yddoc/rpa", "text": "影刀RPA", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/help", "text": "帮助文档", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/快速入门/01 界面功能块介绍.html", "text": "快速入门", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/711663683285495808", "text": "界面功能块介绍", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/快速入门/02 应用搭建的一般流程.html", "text": "应用搭建的一般流程", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/快速入门/03 应用搭建的基本概念.html", "text": "应用搭建的基本概念", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/711648997775491072", "text": "功能文档", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/711641172621627392", "text": "元素捕获", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/功能文档/应用运行相关/应用运行的方式.html", "text": "应用运行相关", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/711725328276783104", "text": "用户设置", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/指令文档/条件判断/if条件.html", "text": "指令文档", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/指令文档/循环/for次数循环.html", "text": "循环", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/指令文档/等待/等待说明.html", "text": "等待", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/接口文档/package.html", "text": "接口文档", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/接口文档/xbot·web/xbot·web.html", "text": "xbot.web", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/接口文档/xbot·win32/xbot·win32.html", "text": "xbot.win32", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/接口文档/xbot·excel/xbot·excel.html", "text": "xbot.excel", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/专题文档/python基础/变量.html", "text": "专题文档", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/710947295618007040", "text": "编码版使用说明", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/710933855642267648", "text": "自定义指令使用说明", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/专题文档/移动端自动化操作教程/手机自动化应用搭建流程.html", "text": "移动端自动化搭建流程", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/常见问题/影刀新手faq.html", "text": "常见问题", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E5%BD%B1%E5%88%80%E6%96%B0%E6%89%8Bfaq.html", "text": "影刀新手 FAQ", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/712487845456834560", "text": "未找到元素", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/常见问题/网页自动化常见问题/区分网页输入框，复选框，下拉框.html", "text": "网页自动化常见问题", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/管理文档/企业账号功能.html", "text": "管理文档", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/管理文档/企业控制台/企业控制台使用说明.html", "text": "企业控制台", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/language/zh-cn/管理文档/调度管理/调度总体使用说明.html", "text": "调度管理", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/710438476072747008", "text": "解决方案", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/710409639833317376", "text": "如何调试影刀应用", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/yddoc/rpa/710446580844957696", "text": "网页懒加载场景和解决方案", "title": "", "base_domain": "yingdao.com"}, {"href": "https://www.yingdao.com/community/homePage/?helpcenter", "text": "去社区提问", "title": "", "base_domain": "yingdao.com"}], "external": []}, "downloaded_files": null, "js_execution_result": null, "screenshot": null, "pdf": null, "mhtml": null, "extracted_content": null, "metadata": {"title": "影刀帮助中心", "description": null, "keywords": null, "author": null, "og:影刀帮助中心": "影刀帮助中心"}, "error_message": "", "session_id": null, "response_headers": {"cache-control": "private, no-cache, no-store, max-age=0, must-revalidate", "content-encoding": "gzip", "content-type": "text/html; charset=utf-8", "date": "Sat, 05 Jul 2025 07:23:29 GMT", "etag": "\"ctvho1w3khemm\"", "server": "YdServer_v1.2.0", "strict-transport-security": "max-age=15724800; includeSubDomains", "vary": "Accept-Encoding", "x-middleware-rewrite": "/yddoc/rpa", "x-powered-by": "Next.js"}, "status_code": 200, "ssl_certificate": null, "dispatch_result": null, "redirected_url": "https://www.yingdao.com/yddoc/rpa", "network_requests": null, "console_messages": null, "tables": [], "markdown": {"raw_markdown": "[![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/3f3cc64d-180c-4dd2-a537-7f564e9b16c8/header_logo.svg)影刀RPA](https://www.yingdao.com/yddoc/rpa)\n快速寻找答案\n快速开始\n影刀概述\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/397cff24-9114-4ef8-9076-3f094a436895/快速入门.png)快速入门\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/6e79cca8-4a66-4a80-9096-6a339328a922/功能文档.png)功能文档\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/90ad038e-8550-4201-a1cf-99839f1350dd/指令文档.png)指令文档\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/d417bad4-9f73-4a16-b6e7-78e84b0c8a24/接口文档.png)接口文档\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/f1277d16-0234-4484-bb43-a6c1b915ba19/常见问题.png)常见问题\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/ee145267-c523-404a-a5f8-464885afa689/API.png)开放API\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/b23157bc-7891-4cd1-8aef-59fbc772bae1/管理文档.png)管理文档\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/b5ed3d11-024f-45cf-aa9f-74ddc6cebf2c/专题文档.png)专题文档\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/50d3349d-784a-4f63-a95d-23999c798cb9/icon.svg)解决方案\n  1. [首页](https://www.yingdao.com/yddoc/rpa)\n  2. >\n  3. [帮助文档](https://www.yingdao.com/yddoc/rpa/help)\n\n\n  * ![](https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/image/20220401093620/4cd91c34e675f59671fc16603597b5ec.png)\n趣味闯关，成为认证工程师\n\n\n  * [![](https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216113443/0df9d547a780f1566eac5132837750fe.png)快速入门](https://www.yingdao.com/yddoc/language/zh-cn/快速入门/01 界面功能块介绍.html)\n[ 界面功能块介绍](https://www.yingdao.com/yddoc/rpa/711663683285495808)[ 应用搭建的一般流程](https://www.yingdao.com/yddoc/language/zh-cn/快速入门/02 应用搭建的一般流程.html)[ 应用搭建的基本概念](https://www.yingdao.com/yddoc/language/zh-cn/快速入门/03 应用搭建的基本概念.html)\n[更多>>](https://www.yingdao.com/yddoc/language/zh-cn/快速入门/01 界面功能块介绍.html)\n  * [![](https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216114137/5fcfc68214a75bd827511598254bfdc0.png)功能文档](https://www.yingdao.com/yddoc/rpa/711648997775491072)\n[ 元素捕获](https://www.yingdao.com/yddoc/rpa/711641172621627392)[ 应用运行相关](https://www.yingdao.com/yddoc/language/zh-cn/功能文档/应用运行相关/应用运行的方式.html)[ 用户设置](https://www.yingdao.com/yddoc/rpa/711725328276783104)\n[更多>>](https://www.yingdao.com/yddoc/rpa/711648997775491072)\n  * [![](https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216114301/cdfd16e9c9e7af0f5431fce6cb80bb8b.png)指令文档](https://www.yingdao.com/yddoc/language/zh-cn/指令文档/条件判断/if条件.html)\n[ 条件判断](https://www.yingdao.com/yddoc/language/zh-cn/指令文档/条件判断/if条件.html)[ 循环](https://www.yingdao.com/yddoc/language/zh-cn/指令文档/循环/for次数循环.html)[ 等待](https://www.yingdao.com/yddoc/language/zh-cn/指令文档/等待/等待说明.html)\n[更多>>](https://www.yingdao.com/yddoc/language/zh-cn/指令文档/条件判断/if条件.html)\n  * [![](https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216113443/e285c1407698dfdb5fc9d9f4fe10fc85.png)接口文档](https://www.yingdao.com/yddoc/language/zh-cn/接口文档/package.html)\n[ xbot.web](https://www.yingdao.com/yddoc/language/zh-cn/接口文档/xbot·web/xbot·web.html)[ xbot.win32](https://www.yingdao.com/yddoc/language/zh-cn/接口文档/xbot·win32/xbot·win32.html)[ xbot.excel](https://www.yingdao.com/yddoc/language/zh-cn/接口文档/xbot·excel/xbot·excel.html)\n[更多>>](https://www.yingdao.com/yddoc/language/zh-cn/接口文档/package.html)\n  * [![](https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216114137/77967011a753d957b064c579e831167f.png)专题文档](https://www.yingdao.com/yddoc/language/zh-cn/专题文档/python基础/变量.html)\n[ 编码版使用说明](https://www.yingdao.com/yddoc/rpa/710947295618007040)[ 自定义指令使用说明](https://www.yingdao.com/yddoc/rpa/710933855642267648)[ 移动端自动化搭建流程](https://www.yingdao.com/yddoc/language/zh-cn/专题文档/移动端自动化操作教程/手机自动化应用搭建流程.html)\n[更多>>](https://www.yingdao.com/yddoc/language/zh-cn/专题文档/python基础/变量.html)\n  * [![](https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216113443/c76c950d938f96cebefe3ad6437d1b88.png)常见问题](https://www.yingdao.com/yddoc/language/zh-cn/常见问题/影刀新手faq.html)\n[ 影刀新手 FAQ](https://www.yingdao.com/yddoc/language/zh-cn/%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E5%BD%B1%E5%88%80%E6%96%B0%E6%89%8Bfaq.html)[ 未找到元素](https://www.yingdao.com/yddoc/rpa/712487845456834560)[ 网页自动化常见问题](https://www.yingdao.com/yddoc/language/zh-cn/常见问题/网页自动化常见问题/区分网页输入框，复选框，下拉框.html)\n[更多>>](https://www.yingdao.com/yddoc/language/zh-cn/常见问题/影刀新手faq.html)\n  * [![](https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216113443/57948d2e92d602698df6ed15b0a7dfb2.png)管理文档](https://www.yingdao.com/yddoc/language/zh-cn/管理文档/企业账号功能.html)\n[ 企业控制台](https://www.yingdao.com/yddoc/language/zh-cn/管理文档/企业控制台/企业控制台使用说明.html)[ 企业账号功能](https://www.yingdao.com/yddoc/language/zh-cn/管理文档/企业账号功能.html)[ 调度管理](https://www.yingdao.com/yddoc/language/zh-cn/管理文档/调度管理/调度总体使用说明.html)\n[更多>>](https://www.yingdao.com/yddoc/language/zh-cn/管理文档/企业账号功能.html)\n  * [![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/50d3349d-784a-4f63-a95d-23999c798cb9/icon.svg)解决方案](https://www.yingdao.com/yddoc/rpa/710438476072747008)\n[ 网页弹窗处理](https://www.yingdao.com/yddoc/rpa/710438476072747008)[ 如何调试影刀应用](https://www.yingdao.com/yddoc/rpa/710409639833317376)[ 网页懒加载场景和解决方案](https://www.yingdao.com/yddoc/rpa/710446580844957696)\n[更多>>](https://www.yingdao.com/yddoc/rpa/710438476072747008)\n\n\n问题没有解决?[去社区提问](https://www.yingdao.com/community/homePage/?helpcenter)\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/ai_assistant/1721040169602/1bca9be3-2267-4bfb-80d6-c4aed68cc1ed)\n", "markdown_with_citations": "![⟨1⟩影刀RPA](https://www.yingdao.com/yddoc/rpa)\n快速寻找答案\n快速开始\n影刀概述\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/397cff24-9114-4ef8-9076-3f094a436895/快速入门.png)快速入门\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/6e79cca8-4a66-4a80-9096-6a339328a922/功能文档.png)功能文档\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/90ad038e-8550-4201-a1cf-99839f1350dd/指令文档.png)指令文档\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/d417bad4-9f73-4a16-b6e7-78e84b0c8a24/接口文档.png)接口文档\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/f1277d16-0234-4484-bb43-a6c1b915ba19/常见问题.png)常见问题\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/ee145267-c523-404a-a5f8-464885afa689/API.png)开放API\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/b23157bc-7891-4cd1-8aef-59fbc772bae1/管理文档.png)管理文档\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/b5ed3d11-024f-45cf-aa9f-74ddc6cebf2c/专题文档.png)专题文档\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/50d3349d-784a-4f63-a95d-23999c798cb9/icon.svg)解决方案\n  1. 首页⟨2⟩\n  2. >\n  3. 帮助文档⟨3⟩\n\n\n  * ![](https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/image/20220401093620/4cd91c34e675f59671fc16603597b5ec.png)\n趣味闯关，成为认证工程师\n\n\n  * ![⟨4⟩快速入门](https://www.yingdao.com/yddoc/language/zh-cn/快速入门/01 界面功能块介绍.html)\n 界面功能块介绍⟨5⟩ 应用搭建的一般流程⟨6⟩ 应用搭建的基本概念⟨7⟩\n更多>>⟨8⟩\n  * ![⟨9⟩功能文档](https://www.yingdao.com/yddoc/rpa/711648997775491072)\n 元素捕获⟨10⟩ 应用运行相关⟨11⟩ 用户设置⟨12⟩\n更多>>⟨13⟩\n  * ![⟨14⟩指令文档](https://www.yingdao.com/yddoc/language/zh-cn/指令文档/条件判断/if条件.html)\n 条件判断⟨15⟩ 循环⟨16⟩ 等待⟨17⟩\n更多>>⟨15⟩\n  * ![⟨18⟩接口文档](https://www.yingdao.com/yddoc/language/zh-cn/接口文档/package.html)\n xbot.web⟨19⟩ xbot.win32⟨20⟩ xbot.excel⟨21⟩\n更多>>⟨22⟩\n  * ![⟨23⟩专题文档](https://www.yingdao.com/yddoc/language/zh-cn/专题文档/python基础/变量.html)\n 编码版使用说明⟨24⟩ 自定义指令使用说明⟨25⟩ 移动端自动化搭建流程⟨26⟩\n更多>>⟨27⟩\n  * ![⟨28⟩常见问题](https://www.yingdao.com/yddoc/language/zh-cn/常见问题/影刀新手faq.html)\n 影刀新手 FAQ⟨29⟩ 未找到元素⟨30⟩ 网页自动化常见问题⟨31⟩\n更多>>⟨32⟩\n  * ![⟨33⟩管理文档](https://www.yingdao.com/yddoc/language/zh-cn/管理文档/企业账号功能.html)\n 企业控制台⟨34⟩ 企业账号功能⟨35⟩ 调度管理⟨36⟩\n更多>>⟨35⟩\n  * ![⟨37⟩解决方案](https://www.yingdao.com/yddoc/rpa/710438476072747008)\n 网页弹窗处理⟨38⟩ 如何调试影刀应用⟨39⟩ 网页懒加载场景和解决方案⟨40⟩\n更多>>⟨38⟩\n\n\n问题没有解决?去社区提问⟨41⟩\n![](https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/ai_assistant/1721040169602/1bca9be3-2267-4bfb-80d6-c4aed68cc1ed)\n", "references_markdown": "\n\n## References\n\n⟨1⟩ https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/3f3cc64d-180c-4dd2-a537-7f564e9b16c8/header_logo.svg: ![\n⟨2⟩ https://www.yingdao.com/yddoc/rpa: 首页\n⟨3⟩ https://www.yingdao.com/yddoc/rpa/help: 帮助文档\n⟨4⟩ https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216113443/0df9d547a780f1566eac5132837750fe.png: ![\n⟨5⟩ https://www.yingdao.com/yddoc/rpa/711663683285495808:  界面功能块介绍\n⟨6⟩ https://www.yingdao.com/yddoc/language/zh-cn/快速入门/02 应用搭建的一般流程.html:  应用搭建的一般流程\n⟨7⟩ https://www.yingdao.com/yddoc/language/zh-cn/快速入门/03 应用搭建的基本概念.html:  应用搭建的基本概念\n⟨8⟩ https://www.yingdao.com/yddoc/language/zh-cn/快速入门/01 界面功能块介绍.html: 更多>>\n⟨9⟩ https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216114137/5fcfc68214a75bd827511598254bfdc0.png: ![\n⟨10⟩ https://www.yingdao.com/yddoc/rpa/711641172621627392:  元素捕获\n⟨11⟩ https://www.yingdao.com/yddoc/language/zh-cn/功能文档/应用运行相关/应用运行的方式.html:  应用运行相关\n⟨12⟩ https://www.yingdao.com/yddoc/rpa/711725328276783104:  用户设置\n⟨13⟩ https://www.yingdao.com/yddoc/rpa/711648997775491072: 更多>>\n⟨14⟩ https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216114301/cdfd16e9c9e7af0f5431fce6cb80bb8b.png: ![\n⟨15⟩ https://www.yingdao.com/yddoc/language/zh-cn/指令文档/条件判断/if条件.html:  条件判断\n⟨16⟩ https://www.yingdao.com/yddoc/language/zh-cn/指令文档/循环/for次数循环.html:  循环\n⟨17⟩ https://www.yingdao.com/yddoc/language/zh-cn/指令文档/等待/等待说明.html:  等待\n⟨18⟩ https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216113443/e285c1407698dfdb5fc9d9f4fe10fc85.png: ![\n⟨19⟩ https://www.yingdao.com/yddoc/language/zh-cn/接口文档/xbot·web/xbot·web.html:  xbot.web\n⟨20⟩ https://www.yingdao.com/yddoc/language/zh-cn/接口文档/xbot·win32/xbot·win32.html:  xbot.win32\n⟨21⟩ https://www.yingdao.com/yddoc/language/zh-cn/接口文档/xbot·excel/xbot·excel.html:  xbot.excel\n⟨22⟩ https://www.yingdao.com/yddoc/language/zh-cn/接口文档/package.html: 更多>>\n⟨23⟩ https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216114137/77967011a753d957b064c579e831167f.png: ![\n⟨24⟩ https://www.yingdao.com/yddoc/rpa/710947295618007040:  编码版使用说明\n⟨25⟩ https://www.yingdao.com/yddoc/rpa/710933855642267648:  自定义指令使用说明\n⟨26⟩ https://www.yingdao.com/yddoc/language/zh-cn/专题文档/移动端自动化操作教程/手机自动化应用搭建流程.html:  移动端自动化搭建流程\n⟨27⟩ https://www.yingdao.com/yddoc/language/zh-cn/专题文档/python基础/变量.html: 更多>>\n⟨28⟩ https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216113443/c76c950d938f96cebefe3ad6437d1b88.png: ![\n⟨29⟩ https://www.yingdao.com/yddoc/language/zh-cn/%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/%E5%BD%B1%E5%88%80%E6%96%B0%E6%89%8Bfaq.html:  影刀新手 FAQ\n⟨30⟩ https://www.yingdao.com/yddoc/rpa/712487845456834560:  未找到元素\n⟨31⟩ https://www.yingdao.com/yddoc/language/zh-cn/常见问题/网页自动化常见问题/区分网页输入框，复选框，下拉框.html:  网页自动化常见问题\n⟨32⟩ https://www.yingdao.com/yddoc/language/zh-cn/常见问题/影刀新手faq.html: 更多>>\n⟨33⟩ https://winrobot-pub-a-dev.oss-cn-hangzhou.aliyuncs.com/image/20220216113443/57948d2e92d602698df6ed15b0a7dfb2.png: ![\n⟨34⟩ https://www.yingdao.com/yddoc/language/zh-cn/管理文档/企业控制台/企业控制台使用说明.html:  企业控制台\n⟨35⟩ https://www.yingdao.com/yddoc/language/zh-cn/管理文档/企业账号功能.html:  企业账号功能\n⟨36⟩ https://www.yingdao.com/yddoc/language/zh-cn/管理文档/调度管理/调度总体使用说明.html:  调度管理\n⟨37⟩ https://xybot-oss-1302949341.cos.ap-shanghai.myqcloud.com/yddoc/rpa/asset/global/50d3349d-784a-4f63-a95d-23999c798cb9/icon.svg: ![\n⟨38⟩ https://www.yingdao.com/yddoc/rpa/710438476072747008:  网页弹窗处理\n⟨39⟩ https://www.yingdao.com/yddoc/rpa/710409639833317376:  如何调试影刀应用\n⟨40⟩ https://www.yingdao.com/yddoc/rpa/710446580844957696:  网页懒加载场景和解决方案\n⟨41⟩ https://www.yingdao.com/community/homePage/?helpcenter: 去社区提问\n", "fit_markdown": "", "fit_html": ""}}], "server_processing_time_s": 1.91758394241333, "server_memory_delta_mb": 5.05078125, "server_peak_memory_mb": 130.2734375}
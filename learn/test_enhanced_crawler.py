import asyncio
from crawl4ai import Async<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rowserConfig, CrawlerRunConfig
from urllib.parse import urljoin

async def crawl_yingdao_interface_docs():
    # 配置浏览器启用JS和会话保持
    browser_cfg = BrowserConfig(
        headless=True,
        java_script_enabled=True,  # 必须启用JS[<sup data-citation='{&quot;id&quot;:1,&quot;url&quot;:&quot;https://docs.crawl4ai.com/core/quickstart/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/core/quickstart/&quot;,&quot;content&quot;:&quot;type: attribute, attribute: src, , , browserconfig = BrowserConfigheadless=True, javascriptenabled=True jsclicktabs = async = const tabs = documentquerySelectorAllsectioncharge-methodology tabs-menu-3&quot;}'>1</sup>](https://docs.crawl4ai.com/core/quickstart/)
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        session_id="yingdao_interface"  # 保持会话[<sup data-citation='{&quot;id&quot;:2,&quot;url&quot;:&quot;https://docs.crawl4ai.com/advanced/session-management/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/advanced/session-management/&quot;,&quot;content&quot;:&quot;allcommits = Define extraction schema schema = name: Commit Extractor, baseSelector: liBox-sc-g0xbh4-0, fields: name: title, selector: h4markdown-title, type: text , extractionstrategy = JsonCssExtrac&quot;}'>2</sup>](https://docs.crawl4ai.com/advanced/session-management/)[<sup data-citation='{&quot;id&quot;:5,&quot;url&quot;:&quot;https://docs.crawl4ai.com/advanced/session-management/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/advanced/session-management/&quot;,&quot;content&quot;:&quot;range3: config = CrawlerRunConfig url=url, sessionid=sessionid, extractionstrategy=extractionstrategy, jscode=jsnextpage if page 0 else None, waitfor=waitfor if page 0 else None, jsonly=page 0, cachem&quot;}'>5</sup>](https://docs.crawl4ai.com/advanced/session-management/)
    )

    # 定义提取策略：通过标题判断是否属于接口文档
    js_extract_links = """
    () => {
        const links = [];
        document.querySelectorAll('a[href^="/yddoc/rpa"]').forEach(a => {
            if (a.textContent.includes('接口文档') || a.closest('.menu-item')?.textContent.includes('接口文档')) {
                links.push({url: a.href, title: a.title || a.textContent});
            }
        });
        return links;
    }
    """

    async with AsyncWebCrawler(config=browser_cfg) as crawler:
        # 第一步：获取所有接口文档链接
        config = CrawlerRunConfig(
            js_code=js_extract_links,  # 执行JS提取链接[<sup data-citation='{&quot;id&quot;:1,&quot;url&quot;:&quot;https://docs.crawl4ai.com/core/quickstart/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/core/quickstart/&quot;,&quot;content&quot;:&quot;type: attribute, attribute: src, , , browserconfig = BrowserConfigheadless=True, javascriptenabled=True jsclicktabs = async = const tabs = documentquerySelectorAllsectioncharge-methodology tabs-menu-3&quot;}'>1</sup>](https://docs.crawl4ai.com/core/quickstart/)
            wait_for="() => document.querySelector('a[href^=\"/yddoc/rpa\"]')",  # 等待动态加载[<sup data-citation='{&quot;id&quot;:2,&quot;url&quot;:&quot;https://docs.crawl4ai.com/advanced/session-management/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/advanced/session-management/&quot;,&quot;content&quot;:&quot;allcommits = Define extraction schema schema = name: Commit Extractor, baseSelector: liBox-sc-g0xbh4-0, fields: name: title, selector: h4markdown-title, type: text , extractionstrategy = JsonCssExtrac&quot;}'>2</sup>](https://docs.crawl4ai.com/advanced/session-management/)
            cache_mode="bypass"
        )
        result = await crawler.arun("https://www.yingdao.com/yddoc", config=config)
        
        if not result.success:
            print(f"初始页面爬取失败: {result.error}")
            return

        # 解析JS返回的链接数据
        try:
            interface_links = eval(result.extracted_content)  # 转换为列表[<sup data-citation='{&quot;id&quot;:2,&quot;url&quot;:&quot;https://docs.crawl4ai.com/advanced/session-management/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/advanced/session-management/&quot;,&quot;content&quot;:&quot;allcommits = Define extraction schema schema = name: Commit Extractor, baseSelector: liBox-sc-g0xbh4-0, fields: name: title, selector: h4markdown-title, type: text , extractionstrategy = JsonCssExtrac&quot;}'>2</sup>](https://docs.crawl4ai.com/advanced/session-management/)
            print(f"发现 {len(interface_links)} 个接口文档链接")
        except:
            print("链接提取失败，请检查JS代码")
            return

        # 第二步：爬取每个子页面
        for link in interface_links:
            full_url = urljoin("https://www.yingdao.com", link['url'])
            print(f"正在爬取: {link['title']} ({full_url})")
            
            page_config = CrawlerRunConfig(
                css_selector="main.content",  # 聚焦正文[<sup data-citation='{&quot;id&quot;:6,&quot;url&quot;:&quot;https://docs.crawl4ai.com/api/arun/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/api/arun/&quot;,&quot;content&quot;:&quot;covered: 1 Crawling the main content region, ignoring external links 2 Running JavaScript to click “show-more” 3 Waiting for “loaded-block” to appear 4 Generating a screenshot PDF of the final page 5 &quot;}'>6</sup>](https://docs.crawl4ai.com/api/arun/)
                wait_for="() => document.title.includes('影刀帮助中心')",  # 等待标题加载[<sup data-citation='{&quot;id&quot;:3,&quot;url&quot;:&quot;https://docs.crawl4ai.com/advanced/session-management/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/advanced/session-management/&quot;,&quot;content&quot;:&quot;onexecutionstarted jsnextpage = documentquerySelectorapagination-nextclick; for page in range3: config = CrawlerRunConfig url=url, sessionid=sessionid, jscode=jsnextpage if page 0 else None, cssselect&quot;}'>3</sup>](https://docs.crawl4ai.com/advanced/session-management/)
                word_count_threshold=50  # 过滤低内容页面[<sup data-citation='{&quot;id&quot;:6,&quot;url&quot;:&quot;https://docs.crawl4ai.com/api/arun/&quot;,&quot;title&quot;:&quot;https://docs.crawl4ai.com/api/arun/&quot;,&quot;content&quot;:&quot;covered: 1 Crawling the main content region, ignoring external links 2 Running JavaScript to click “show-more” 3 Waiting for “loaded-block” to appear 4 Generating a screenshot PDF of the final page 5 &quot;}'>6</sup>](https://docs.crawl4ai.com/api/arun/)
            )
            page_result = await crawler.arun(full_url, config=page_config)
            
            if page_result.success:
                filename = f"{link['title'].replace(' ', '_')}.md"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(page_result.markdown)
                print(f"✅ 已保存: {filename}")
            else:
                print(f"❌ 失败: {link['title']} - {page_result.error}")

asyncio.run(crawl_yingdao_interface_docs())

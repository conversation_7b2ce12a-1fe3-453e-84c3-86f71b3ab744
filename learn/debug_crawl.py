import asyncio
from crawl4ai import Async<PERSON><PERSON><PERSON>raw<PERSON>, CrawlerRunConfig

async def debug_crawl():
    test_url = "https://docs.crawl4ai.com/core/quickstart/"
    
    # 测试不同的配置
    configs = [
        {"name": "默认配置", "config": CrawlerRunConfig()},
        {"name": "main.content选择器", "config": CrawlerRunConfig(css_selector="main.content")},
        {"name": "main选择器", "config": CrawlerRunConfig(css_selector="main")},
        {"name": ".content选择器", "config": CrawlerRunConfig(css_selector=".content")},
        {"name": "article选择器", "config": CrawlerRunConfig(css_selector="article")},
        {"name": "无选择器+低阈值", "config": CrawlerRunConfig(word_count_threshold=1)},
    ]
    
    async with AsyncWebCrawler() as crawler:
        for test in configs:
            print(f"\n=== 测试: {test['name']} ===")
            try:
                result = await crawler.arun(url=test_url, config=test['config'])
                if result.success:
                    content_length = len(result.markdown.strip())
                    print(f"✅ 成功 - 内容长度: {content_length} 字符")
                    if content_length > 0:
                        print(f"前100字符: {result.markdown[:100]}")
                    else:
                        print("⚠️ 内容为空")
                        # 打印HTML的前500字符来调试
                        print(f"HTML前500字符: {result.html[:500]}")
                else:
                    print(f"❌ 失败: {result.error}")
            except Exception as e:
                print(f"⚠️ 异常: {str(e)}")

asyncio.run(debug_crawl())

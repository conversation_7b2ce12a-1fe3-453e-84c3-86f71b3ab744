我想要增强 `AsyncPlaywrightCrawlerStrategy` 以在爬取过程中可选地捕获网络请求和控制台消息，并将它们存储在最终的 `CrawlResult` 中。

以下是跨相关文件的建议更改细分：

**1. 配置 (`crawl4ai/async_configs.py`)**

*   **目标：** 向 `CrawlerRunConfig` 添加标志以启用/禁用捕获。
*   **更改：**
    *   向 `CrawlerRunConfig` 添加两个新的布尔属性：
        *   `capture_network_requests: bool = False`
        *   `capture_console_messages: bool = False`
    *   更新 `__init__`、`from_kwargs`、`to_dict`，并隐式更新 `clone`/`dump`/`load` 以包含这些新属性。

```python
# ==== 文件: crawl4ai/async_configs.py ====
# ... (导入) ...

class CrawlerRunConfig():
    # ... (现有属性) ...

    # 新增: 网络和控制台捕获参数
    capture_network_requests: bool = False
    capture_console_messages: bool = False

    # 实验性参数
    experimental: Dict[str, Any] = None,

    def __init__(
        self,
        # ... (现有参数) ...

        # 新增: 网络和控制台捕获参数
        capture_network_requests: bool = False,
        capture_console_messages: bool = False,

        # 实验性参数
        experimental: Dict[str, Any] = None,
    ):
        # ... (现有赋值) ...

        # 新增: 分配新参数
        self.capture_network_requests = capture_network_requests
        self.capture_console_messages = capture_console_messages

        # 实验性参数
        self.experimental = experimental or {}

        # ... (__init__ 的其余部分) ...

    @staticmethod
    def from_kwargs(kwargs: dict) -> "CrawlerRunConfig":
        return CrawlerRunConfig(
            # ... (现有 kwargs 获取) ...

            # 新增: 获取新参数
            capture_network_requests=kwargs.get("capture_network_requests", False),
            capture_console_messages=kwargs.get("capture_console_messages", False),

            # 实验性参数
            experimental=kwargs.get("experimental"),
        )

    def to_dict(self):
        return {
            # ... (现有字典条目) ...

            # 新增: 将新参数添加到字典
            "capture_network_requests": self.capture_network_requests,
            "capture_console_messages": self.capture_console_messages,

            "experimental": self.experimental,
        }

    # clone()、dump()、load() 如果依赖于 to_dict() 和 from_kwargs() 应该自动工作
    # 或者序列化逻辑正确处理所有属性。
```

**2. 数据模型 (`crawl4ai/models.py`)**

*   **目标：** 添加字段以在响应/结果对象中存储捕获的数据。
*   **更改：**
    *   向 `AsyncCrawlResponse` 添加 `network_requests: Optional[List[Dict[str, Any]]] = None` 和 `console_messages: Optional[List[Dict[str, Any]]] = None`。
    *   向 `CrawlResult` 添加相同的字段。

```python
# ==== 文件: crawl4ai/models.py ====
# ... (导入) ...

# ... (现有数据类/模型) ...

class AsyncCrawlResponse(BaseModel):
    html: str
    response_headers: Dict[str, str]
    js_execution_result: Optional[Dict[str, Any]] = None
    status_code: int
    screenshot: Optional[str] = None
    pdf_data: Optional[bytes] = None
    get_delayed_content: Optional[Callable[[Optional[float]], Awaitable[str]]] = None
    downloaded_files: Optional[List[str]] = None
    ssl_certificate: Optional[SSLCertificate] = None
    redirected_url: Optional[str] = None
    # 新增: 捕获数据的字段
    network_requests: Optional[List[Dict[str, Any]]] = None
    console_messages: Optional[List[Dict[str, Any]]] = None

    class Config:
        arbitrary_types_allowed = True

# ... (现有模型如 MediaItem、Link 等) ...

class CrawlResult(BaseModel):
    url: str
    html: str
    success: bool
    cleaned_html: Optional[str] = None
    media: Dict[str, List[Dict]] = {}
    links: Dict[str, List[Dict]] = {}
    downloaded_files: Optional[List[str]] = None
    js_execution_result: Optional[Dict[str, Any]] = None
    screenshot: Optional[str] = None
    pdf: Optional[bytes] = None
    mhtml: Optional[str] = None # 基于提供的 models.py 添加 mhtml
    _markdown: Optional[MarkdownGenerationResult] = PrivateAttr(default=None)
    extracted_content: Optional[str] = None
    metadata: Optional[dict] = None
    error_message: Optional[str] = None
    session_id: Optional[str] = None
    response_headers: Optional[dict] = None
    status_code: Optional[int] = None
    ssl_certificate: Optional[SSLCertificate] = None
    dispatch_result: Optional[DispatchResult] = None
    redirected_url: Optional[str] = None
    # 新增: 捕获数据的字段
    network_requests: Optional[List[Dict[str, Any]]] = None
    console_messages: Optional[List[Dict[str, Any]]] = None

    class Config:
        arbitrary_types_allowed = True

    # ... (现有 __init__、属性、markdown 兼容性的 model_dump) ...

# ... (模型的其余部分) ...
```

**3. 爬虫策略 (`crawl4ai/async_crawler_strategy.py`)**

*   **目标：** 在 `AsyncPlaywrightCrawlerStrategy._crawl_web` 中实现实际的捕获逻辑。
*   **更改：**
    *   在 `_crawl_web` 内部，初始化空列表 `captured_requests = []` 和 `captured_console = []`。
    *   根据 `config.capture_network_requests` 和 `config.capture_console_messages` 标志有条件地附加 Playwright 事件监听器 (`page.on(...)`)。
    *   为这些监听器定义处理函数，以提取相关数据并将其附加到相应的列表中。包含时间戳。
    *   在方法结束时将捕获的列表传递给 `AsyncCrawlResponse` 构造函数。

```python
# ==== 文件: crawl4ai/async_crawler_strategy.py ====
# ... (导入) ...
import time # 确保导入 time

class AsyncPlaywrightCrawlerStrategy(AsyncCrawlerStrategy):
    # ... (现有方法如 __init__、start、close 等) ...

    async def _crawl_web(
        self, url: str, config: CrawlerRunConfig
    ) -> AsyncCrawlResponse:
        """
        使用指定配置爬取网页 URL 的内部方法。
        包括可选的网络和控制台捕获。 # 修改的文档字符串
        """
        config.url = url
        response_headers = {}
        execution_result = None
        status_code = None
        redirected_url = url

        # 为新爬取重置下载文件列表
        self._downloaded_files = []

        # 初始化捕获列表 - 重要: 每次爬取都重置
        captured_requests: List[Dict[str, Any]] = []
        captured_console: List[Dict[str, Any]] = []

        # 处理用户代理 ... (现有代码) ...

        # 获取会话页面
        page, context = await self.browser_manager.get_page(crawlerRunConfig=config)

        # ... (现有的 cookies、navigator 覆盖、hooks 代码) ...

        # --- 设置捕获监听器 ---
        # 注意: 这些监听器在 page.goto() 之前附加

        # 网络请求捕获
        if config.capture_network_requests:
            async def handle_request_capture(request):
                try:
                    post_data_str = None
                    try:
                        # 对大型 post 数据要谨慎
                        post_data = request.post_data_buffer
                        if post_data:
                             # 尝试解码，回退到 base64 或大小指示
                             try:
                                 post_data_str = post_data.decode('utf-8', errors='replace')
                             except UnicodeDecodeError:
                                 post_data_str = f"[二进制数据: {len(post_data)} 字节]"
                    except Exception:
                        post_data_str = "[检索 post 数据时出错]"

                    captured_requests.append({
                        "event_type": "request",
                        "url": request.url,
                        "method": request.method,
                        "headers": dict(request.headers), # 转换 Header 字典
                        "post_data": post_data_str,
                        "resource_type": request.resource_type,
                        "is_navigation_request": request.is_navigation_request(),
                        "timestamp": time.time()
                    })
                except Exception as e:
                    self.logger.warning(f"捕获请求详情时出错 {request.url}: {e}", tag="CAPTURE")
                    captured_requests.append({"event_type": "request_capture_error", "url": request.url, "error": str(e), "timestamp": time.time()})

            async def handle_response_capture(response):
                try:
                    # 默认情况下避免捕获完整响应体，因为大小/安全原因
                    # security_details = await response.security_details() # 可选: 更多 SSL 信息
                    captured_requests.append({
                        "event_type": "response",
                        "url": response.url,
                        "status": response.status,
                        "status_text": response.status_text,
                        "headers": dict(response.headers), # 转换 Header 字典
                        "from_service_worker": response.from_service_worker,
                        # "security_details": security_details, # 如果需要请取消注释
                        "request_timing": response.request.timing, # 详细时间信息
                        "timestamp": time.time()
                    })
                except Exception as e:
                    self.logger.warning(f"捕获响应详情时出错 {response.url}: {e}", tag="CAPTURE")
                    captured_requests.append({"event_type": "response_capture_error", "url": response.url, "error": str(e), "timestamp": time.time()})

            async def handle_request_failed_capture(request):
                 try:
                    captured_requests.append({
                        "event_type": "request_failed",
                        "url": request.url,
                        "method": request.method,
                        "resource_type": request.resource_type,
                        "failure_text": request.failure.error_text if request.failure else "未知失败",
                        "timestamp": time.time()
                    })
                 except Exception as e:
                    self.logger.warning(f"捕获请求失败详情时出错 {request.url}: {e}", tag="CAPTURE")
                    captured_requests.append({"event_type": "request_failed_capture_error", "url": request.url, "error": str(e), "timestamp": time.time()})

            page.on("request", handle_request_capture)
            page.on("response", handle_response_capture)
            page.on("requestfailed", handle_request_failed_capture)

        # 控制台消息捕获
        if config.capture_console_messages:
            def handle_console_capture(msg):
                 try:
                    location = msg.location()
                    # 尝试将 JSHandle 参数解析为原始值
                    resolved_args = []
                    try:
                        for arg in msg.args:
                            resolved_args.append(arg.json_value()) # 对于复杂对象可能失败
                    except Exception:
                         resolved_args.append("[无法解析 JSHandle 参数]")

                    captured_console.append({
                        "type": msg.type(), # 例如 'log'、'error'、'warning'
                        "text": msg.text(),
                        "args": resolved_args, # 捕获的参数
                        "location": f"{location['url']}:{location['lineNumber']}:{location['columnNumber']}" if location else "N/A",
                        "timestamp": time.time()
                    })
                 except Exception as e:
                    self.logger.warning(f"捕获控制台消息时出错: {e}", tag="CAPTURE")
                    captured_console.append({"type": "console_capture_error", "error": str(e), "timestamp": time.time()})

            def handle_pageerror_capture(err):
                 try:
                    captured_console.append({
                        "type": "error", # 页面错误的一致类型
                        "text": err.message,
                        "stack": err.stack,
                        "timestamp": time.time()
                    })
                 except Exception as e:
                    self.logger.warning(f"捕获页面错误时出错: {e}", tag="CAPTURE")
                    captured_console.append({"type": "pageerror_capture_error", "error": str(e), "timestamp": time.time()})

            page.on("console", handle_console_capture)
            page.on("pageerror", handle_pageerror_capture)
        # --- 结束设置捕获监听器 ---


        # 如果请求，设置控制台日志记录（保持原始日志记录逻辑分离或仔细合并）
        if config.log_console:
            # ... (使用 page.on(...) 的原始 log_console 设置保留在这里) ...
            # 这允许在两个标志都为 True 时同时记录到屏幕*和*捕获到列表
            def log_consol(msg, console_log_type="debug"):
                # ... 现有实现 ...
                pass # 现有代码的占位符

            page.on("console", lambda msg: log_consol(msg, "debug"))
            page.on("pageerror", lambda e: log_consol(e, "error"))


        try:
            # ... (现有的 SSL、下载、goto、等待、JS 执行等代码) ...

            # 获取最终 HTML 内容
            # ... (现有的选择器逻辑或 page.content() 代码) ...
            if config.css_selector:
                # ... 现有选择器逻辑 ...
                html = f"<div class='crawl4ai-result'>\n" + "\n".join(html_parts) + "\n</div>"
            else:
                html = await page.content()

            await self.execute_hook(
                "before_return_html", page=page, html=html, context=context, config=config
            )

            # 处理 PDF 和截图生成
            # ... (现有代码) ...

            # 定义延迟内容获取器
            # ... (现有代码) ...

            # 返回完整响应 - 在这里添加捕获的数据
            return AsyncCrawlResponse(
                html=html,
                response_headers=response_headers,
                js_execution_result=execution_result,
                status_code=status_code,
                screenshot=screenshot_data,
                pdf_data=pdf_data,
                get_delayed_content=get_delayed_content,
                ssl_certificate=ssl_cert,
                downloaded_files=(
                    self._downloaded_files if self._downloaded_files else None
                ),
                redirected_url=redirected_url,
                # 新增: 有条件地传递捕获的数据
                network_requests=captured_requests if config.capture_network_requests else None,
                console_messages=captured_console if config.capture_console_messages else None,
            )

        except Exception as e:
            raise e # 重新抛出原始异常

        finally:
            # 如果没有给出 session_id，我们应该关闭页面
            if not config.session_id:
                # 在关闭前分离监听器以防止关闭期间的潜在错误
                if config.capture_network_requests:
                    page.remove_listener("request", handle_request_capture)
                    page.remove_listener("response", handle_response_capture)
                    page.remove_listener("requestfailed", handle_request_failed_capture)
                if config.capture_console_messages:
                    page.remove_listener("console", handle_console_capture)
                    page.remove_listener("pageerror", handle_pageerror_capture)
                # 如果附加了日志监听器，也要移除它们
                if config.log_console:
                    # 需要找出如何在必要时移除 lambda，
                    # 或确保它们在关闭时不会造成问题。通常，这是可以的。
                    pass

                await page.close()

    # ... (AsyncPlaywrightCrawlerStrategy 方法的其余部分) ...

```

**4. 核心爬虫 (`crawl4ai/async_webcrawler.py`)**

*   **目标：** 确保从 `AsyncCrawlResponse` 捕获的数据传输到最终的 `CrawlResult`。
*   **更改：**
    *   在 `arun` 中，当处理非缓存结果时（在 `if not cached_result or not html:` 块内），在接收到 `async_response` 并调用 `aprocess_html` 获取 `crawl_result` 后，将 `network_requests` 和 `console_messages` 从 `async_response` 复制到 `crawl_result`。

```python
# ==== 文件: crawl4ai/async_webcrawler.py ====
# ... (导入) ...

class AsyncWebCrawler:
    # ... (现有方法) ...

    async def arun(
        self,
        url: str,
        config: CrawlerRunConfig = None,
        **kwargs,
    ) -> RunManyReturn:
        # ... (现有设置、缓存检查) ...

        async with self._lock or self.nullcontext():
            try:
                # ... (现有日志记录、缓存上下文设置) ...

                if cached_result:
                    # ... (现有缓存处理逻辑) ...
                    # 注意: 捕获的网络/控制台通常从缓存中不有用
                    # 确保它们为 None 或空，如果从缓存读取，除非明确存储
                    cached_result.network_requests = cached_result.network_requests or None
                    cached_result.console_messages = cached_result.console_messages or None
                    # ... (缓存逻辑的其余部分) ...

                # 如果需要，获取新内容
                if not cached_result or not html:
                    t1 = time.perf_counter()

                    # ... (现有用户代理更新、robots.txt 检查) ...

                    ##############################
                    # 调用 CrawlerStrategy.crawl #
                    ##############################
                    async_response = await self.crawler_strategy.crawl(
                        url,
                        config=config,
                    )

                    # ... (现有从 async_response 分配 html、screenshot、pdf、js_result) ...

                    t2 = time.perf_counter()
                    # ... (现有日志记录) ...

                    ###############################################################
                    # 处理 HTML 内容，调用 CrawlerStrategy.process_html #
                    ###############################################################
                    crawl_result: CrawlResult = await self.aprocess_html(
                        # ... (现有参数) ...
                    )

                    # --- 从 AsyncCrawlResponse 传输数据到 CrawlResult ---
                    crawl_result.status_code = async_response.status_code
                    crawl_result.redirected_url = async_response.redirected_url or url
                    crawl_result.response_headers = async_response.response_headers
                    crawl_result.downloaded_files = async_response.downloaded_files
                    crawl_result.js_execution_result = js_execution_result
                    crawl_result.ssl_certificate = async_response.ssl_certificate
                    # 新增: 复制捕获的数据
                    crawl_result.network_requests = async_response.network_requests
                    crawl_result.console_messages = async_response.console_messages
                    # ------------------------------------------------------------

                    crawl_result.success = bool(html)
                    crawl_result.session_id = getattr(config, "session_id", None)

                    # ... (现有日志记录) ...

                    # 如果适当，更新缓存
                    if cache_context.should_write() and not bool(cached_result):
                        # crawl_result 现在包括网络/控制台数据（如果捕获）
                        await async_db_manager.acache_url(crawl_result)

                    return CrawlResultContainer(crawl_result)

                else: # 使用了缓存结果
                     # ... (缓存命中的现有日志记录) ...
                    cached_result.success = bool(html)
                    cached_result.session_id = getattr(config, "session_id", None)
                    cached_result.redirected_url = cached_result.redirected_url or url
                    return CrawlResultContainer(cached_result)

            except Exception as e:
                # ... (现有错误处理) ...
                return CrawlResultContainer(
                    CrawlResult(
                        url=url, html="", success=False, error_message=error_message
                    )
                )

    # ... (aprocess_html 关于捕获保持不变) ...

    # ... (arun_many 关于捕获保持不变) ...
```

**更改摘要：**

1.  **配置：** 向 `CrawlerRunConfig` 添加了 `capture_network_requests` 和 `capture_console_messages` 标志。
2.  **模型：** 向 `AsyncCrawlResponse` 和 `CrawlResult` 添加了相应的 `network_requests` 和 `console_messages` 字段（字典列表）。
3.  **策略：** 在 `AsyncPlaywrightCrawlerStrategy._crawl_web` 中实现了条件事件监听器，当标志为 true 时将数据捕获到列表中。在返回的 `AsyncCrawlResponse` 中填充这些字段。在捕获处理程序中添加了基本错误处理。添加了时间戳。
4.  **爬虫：** 修改了 `AsyncWebCrawler.arun` 以将捕获的数据从 `AsyncCrawlResponse` 复制到非缓存获取的最终 `CrawlResult` 中。

这种方法将捕获逻辑包含在 Playwright 策略中，使用清晰的配置标志，并将结果集成到现有的数据流中。数据格式（字典列表）对于存储来自请求/响应/控制台消息的各种信息是灵活的。
